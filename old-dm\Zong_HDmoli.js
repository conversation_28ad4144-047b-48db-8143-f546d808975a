// ignore

//@name:[综] HDmoli
//@webSite:https://www.hdmoli.pro/
//@version:2
//@remark:🍃豆儿出品，不属精品！支持在线播放和网盘
//@codeID:OAxUOePWsN3I7NQfNIO9Il1I1cgolbSa
//@order: A

// ignore




//MARK: 注意
// 直接复制该文件进行扩展开发
// 请保持以下 变量 及 函数 名称不变
// 请勿删减，可以新增

const appConfig = {
    _webSite: 'https://www.hdmoli.pro/',
    /**
     * 网站主页，uz 调用每个函数前都会进行赋值操作
     * 如果不想被改变 请自定义一个变量
     */
    get webSite() {
        return this._webSite
    },
    set webSite(value) {
        this._webSite = value
    },

    _uzTag: '',
    /**
     * 扩展标识，初次加载时，uz 会自动赋值，请勿修改
     * 用于读取环境变量
     */
    get uzTag() {
        return this._uzTag
    },
    set uzTag(value) {
        this._uzTag = value
    },

    // 分类映射
    categories: {
        '1': '电影',
        '2': '剧集',
        '41': '动画'
    },

    // 网盘域名识别
    panDomains: [
        // 夸克网盘
        'pan.quark.cn',
        // UC网盘
        'drive.uc.cn',
        // 阿里云盘
        'alipan.com',
        // 123网盘
        '123684.com', '123865.com', '123912.com', '123pan.com', '123pan.cn',
        // 天翼云盘
        '189.cn'
    ]
}

// 全局变量
let hasShownWelcome = false  // 标记是否已显示欢迎提示

/**
 * 异步获取分类列表的方法。
 * @param {UZArgs} args
 * @returns {@Promise<JSON.stringify(new RepVideoClassList())>}
 */
async function getClassList(args) {
    var backData = new RepVideoClassList()
    try {
        // 首次加载时显示欢迎提示
        if (!hasShownWelcome) {
            hasShownWelcome = true
            toast("🍃豆儿出品，不属精品！", 3)  // 显示3秒
        }

        // 返回固定的分类列表
        Object.keys(appConfig.categories).forEach(id => {
            backData.data.push({
                type_id: id,
                type_name: appConfig.categories[id],
                hasSubclass: false
            })
        })
    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取二级分类列表筛选列表的方法。
 * @param {UZArgs} args
 * @returns {@Promise<JSON.stringify(new RepVideoSubclassList())>}
 */
async function getSubclassList(args) {
    var backData = new RepVideoSubclassList()
    try {
    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取分类视频列表
 * @param {UZArgs} args
 * @returns {@Promise<JSON.stringify(new RepVideoList())>}
 */
async function getVideoList(args) {
    var backData = new RepVideoList()
    try {
        const categoryId = args.url
        let url = `${appConfig.webSite}mlist/index${categoryId}.html`

        // 处理分页
        if (args.page > 1) {
            url = `${appConfig.webSite}mlist/index${categoryId}-${args.page}.html`
        }

        const response = await req(url)
        const $ = cheerio.load(response.data)

        // 解析视频列表 - 只获取分类列表必需的信息
        $('.myui-vodlist li').each((_, element) => {
            const $item = $(element)
            const $link = $item.find('.title a').first()
            const $thumbLink = $item.find('.myui-vodlist__thumb').first()

            if ($link.length > 0) {
                const video = new VideoDetail()

                // 提取视频ID
                const href = $link.attr('href')
                const idMatch = href.match(/index(\d+)\.html/)
                if (idMatch) {
                    video.vod_id = idMatch[1]
                }

                // 视频名称
                video.vod_name = $link.text().trim()

                // 封面图片获取
                if ($thumbLink.length > 0) {
                    const picUrl = $thumbLink.attr('data-original') || $thumbLink.attr('data-src')
                    if (picUrl) {
                        video.vod_pic = picUrl.startsWith('//') ? 'https:' + picUrl : picUrl
                    }
                }

                // 状态标志（右下角显示的更新信息）
                const $picText = $item.find('.pic-text').first()
                if ($picText.length > 0) {
                    video.vod_remarks = $picText.text().trim()
                }

                backData.data.push(video)
            }
        })

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取二级分类视频列表 或 筛选视频列表
 * @param {UZSubclassVideoListArgs} args
 * @returns {@Promise<JSON.stringify(new RepVideoList())>}
 */
async function getSubclassVideoList(args) {
    var backData = new RepVideoList()
    try {
    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取视频详情
 * @param {UZArgs} args
 * @returns {@Promise<JSON.stringify(new RepVideoDetail())>}
 */
async function getVideoDetail(args) {
    var backData = new RepVideoDetail()
    try {
        const videoId = args.url
        const url = `${appConfig.webSite}movie/index${videoId}.html`

        const response = await req(url)
        const $ = cheerio.load(response.data)

        const video = new VideoDetail()
        video.vod_id = videoId

        // 基本信息 - 只获取详情页界面需要的信息
        video.vod_name = $('h1').text().replace(/\s*更新至\d+集|\s*网盘.*$/g, '').trim()

        // 封面图片
        const $mainImg = $('.myui-content__thumb img.lazyload').first()
        if ($mainImg.length > 0) {
            const picUrl = $mainImg.attr('data-original') || $mainImg.attr('data-src')
            if (picUrl && !picUrl.includes('load.png')) {
                video.vod_pic = picUrl.startsWith('//') ? 'https:' + picUrl : picUrl
            }
        }

        // 详细信息解析 - 只获取界面显示的信息
        $('p').each((_, element) => {
            const text = $(element).text()

            // 演员信息（界面显示）
            if (text.includes('演员：')) {
                const match = text.match(/演员：(.+)/)
                if (match) video.vod_actor = match[1].trim()
            }

            // 简介信息（界面显示）
            if (text.includes('简介：')) {
                const match = text.match(/简介：(.+)/)
                if (match) video.vod_content = match[1].trim()
            }
        })

        // 解析播放列表和网盘链接
        const playFromList = []
        const playUrlList = []
        const panUrls = []

        // 按播放线路分别解析
        $('.tab-content .tab-pane').each((index, tabPane) => {
            const $tabPane = $(tabPane)
            const tabId = $tabPane.attr('id')

            // 获取对应的线路名称 - 优先使用p标签中[]内的描述
            let playFromName = `线路${index + 1}`

            // 首先尝试从p标签的描述中提取线路名称
            const $description = $tabPane.find('.text-muted, p').first()
            if ($description.length > 0) {
                const descText = $description.text().trim()

                // 检测是否为伪播放源 - 如果描述包含"无法提供在线观看"，跳过这个线路
                if (descText.includes('无法提供在线观看')) {
                    return  // 跳过这个线路，不添加到播放列表
                }

                const nameMatch = descText.match(/\[([^\]]+)\]/)
                if (nameMatch) {
                    playFromName = nameMatch[1]  // 使用[]中的内容作为线路名称
                }
            }

            // 如果没有找到描述，使用tab标签的文本
            if (playFromName.startsWith('线路')) {
                const $tabLink = $(`.nav-tabs a[href="#${tabId}"]`)
                if ($tabLink.length > 0) {
                    playFromName = $tabLink.text().trim()
                }
            }

            // 解析该线路的播放列表
            const episodeUrls = []
            $tabPane.find('ul li a[href*="/play/"]').each((_, element) => {
                const $link = $(element)
                const episodeName = $link.text().trim()
                const href = $link.attr('href')

                if (href && episodeName) {
                    episodeUrls.push(`${episodeName}$${href}`)
                }
            })

            // 如果该线路有播放链接，添加到列表
            if (episodeUrls.length > 0) {
                playFromList.push(playFromName)
                playUrlList.push(episodeUrls.join('#'))
            }
        })

        // 如果没有找到分线路的播放列表，使用原来的方法作为备用
        if (playFromList.length === 0) {
            const playUrls = []
            $('ul li a[href*="/play/"]').each((_, element) => {
                const $link = $(element)
                const episodeName = $link.text().trim()
                const href = $link.attr('href')

                if (href && episodeName && (episodeName.includes('第') && episodeName.includes('集'))) {
                    playUrls.push(`${episodeName}$${href}`)
                }
            })

            if (playUrls.length > 0) {
                playFromList.push('在线播放')
                playUrlList.push(playUrls.join('#'))
            }
        }

        // 网盘下载链接 - 更全面的网盘域名匹配
        const panSelectors = [
            // 夸克网盘
            'a[href*="pan.quark.cn"]',
            // UC网盘
            'a[href*="drive.uc.cn"]',
            // 阿里云盘
            'a[href*="alipan.com"]',
            // 123网盘
            'a[href*="123684.com"]', 'a[href*="123865.com"]', 'a[href*="123912.com"]',
            'a[href*="123pan.com"]', 'a[href*="123pan.cn"]',
            // 天翼云盘
            'a[href*="189.cn"]'
        ]

        panSelectors.forEach(selector => {
            $(selector).each((_, element) => {
                const href = $(element).attr('href')
                if (href && !panUrls.includes(href)) {
                    panUrls.push(href)
                }
            })
        })

        // 也检查文本中的网盘链接
        $('p, div').each((_, element) => {
            const text = $(element).text()
            const urlRegex = /https?:\/\/[^\s]+/g
            const matches = text.match(urlRegex)
            if (matches) {
                matches.forEach(url => {
                    if (appConfig.panDomains.some(domain => url.includes(domain)) && !panUrls.includes(url)) {
                        panUrls.push(url)
                    }
                })
            }
        })

        // 设置播放信息
        if (playFromList.length > 0) {
            video.vod_play_from = playFromList.join('$$$')
            video.vod_play_url = playUrlList.join('$$$')
        }

        // 设置网盘链接
        video.panUrls = panUrls

        backData.data = video

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取视频的播放地址
 * @param {UZArgs} args
 * @returns {@Promise<JSON.stringify(new RepVideoPlayUrl())>}
 */
async function getVideoPlayUrl(args) {
    var backData = new RepVideoPlayUrl()
    try {
        const playUrl = args.url

        // 如果是相对路径，补全域名
        let fullUrl = playUrl
        if (playUrl.startsWith('/')) {
            fullUrl = appConfig.webSite.replace(/\/$/, '') + playUrl
        }

        // 使用嗅探模式 - UZ软件会自动处理倒计时、跳过按钮并嗅探真实播放地址
        backData.sniffer = {
            url: fullUrl,
            ua: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            timeOut: 30,  // 嗅探超时时间30秒，给倒计时足够时间
            retry: 2      // 重试2次
        }

        // 播放时的请求头
        backData.headers = {
            'Referer': appConfig.webSite,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 搜索视频
 * @param {UZArgs} args
 * @returns {@Promise<JSON.stringify(new RepVideoList())>}
 */
async function searchVideo(args) {
    var backData = new RepVideoList()
    try {
        const searchKey = encodeURIComponent(args.searchWord)
        const page = args.page || 1  // 如果没有页码，默认为1
        let url = `${appConfig.webSite}search.php?page=${page}&searchkey=${searchKey}&searchtype=`

        const response = await req(url)
        const $ = cheerio.load(response.data)

        // 解析搜索结果 - 搜索页面使用不同的HTML结构
        $('.myui-vodlist__media li').each((_, element) => {
            const $item = $(element)
            const $titleLink = $item.find('h4.title a').first()

            if ($titleLink.length > 0) {
                const video = new VideoDetail()

                // 提取视频ID
                const href = $titleLink.attr('href')
                const idMatch = href.match(/index(\d+)\.html/)
                if (idMatch) {
                    video.vod_id = idMatch[1]
                }

                // 视频名称
                video.vod_name = $titleLink.text().trim()

                // 封面图片获取
                const $thumbLink = $item.find('.myui-vodlist__thumb').first()
                if ($thumbLink.length > 0) {
                    const picUrl = $thumbLink.attr('data-original')
                    if (picUrl) {
                        video.vod_pic = picUrl.startsWith('//') ? 'https:' + picUrl : picUrl
                    }
                }

                // 状态标志（更新信息）- 从pic-text获取
                const $picText = $item.find('.pic-text').first()
                if ($picText.length > 0) {
                    video.vod_remarks = $picText.text().trim()
                }

                backData.data.push(video)
            }
        })

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}