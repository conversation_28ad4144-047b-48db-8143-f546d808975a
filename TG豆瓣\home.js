// ignore
import {} from '../../core/uzVideo.js'
import {} from '../../core/uzHome.js'
import {} from '../../core/uz3lib.js'
import {} from '../../core/uzUtils.js'
// ignore

class HomeJS extends UZHome {
    headers = {
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        Referer: 'https://m.douban.com/',
        'User-Agent':
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15',
    }

    /**
     * 获取首页
     * @returns {Promise<RepHome>}
     */
    async getHome() {
        let repData = new RepHome()
        let tuiJian = new HomeTabModel()
        tuiJian.id = '0'
        tuiJian.name = '推荐'
        repData.data.push(tuiJian)

        let findTV = new VideoClass()
        findTV.id = '1'
        findTV.name = '找电视剧'
        findTV.isFilter = true
        repData.data.push(findTV)

        let findMovie = new VideoClass()
        findMovie.id = '2'
        findMovie.name = '找电影'
        findMovie.isFilter = true
        repData.data.push(findMovie)

        let findZY = new VideoClass()
        findZY.id = '3'
        findZY.name = '找综艺'
        findZY.isFilter = true
        repData.data.push(findZY)

        return JSON.stringify(repData)
    }

    /**
     * 获取 tab
     * @param {UZArgs} args
     * @returns {Promise<RepTabList>}
     */
    async getTab(args) {
        let repData = new RepTabList()
        try {
            if (args.url === '0') {
                if (args.page === 1) {
                    await this.getRecommendDataPage1(repData)
                } else {
                    await this.getMoreRecommendData(args.page - 1, repData)
                }
            } else if (args.url === '1') {
                // 找电视剧
                await this.getTVFilterLabsData(repData)
            } else if (args.url === '2') {
                // 找电影
                await this.getMovieFilterLabsData(repData)
            } else if (args.url === '3') {
                // 找综艺
                await this.getZYFilterLabsData(repData)
            }
        } catch (error) {
            repData.error = error.toString()
        }

        return JSON.stringify(repData)
    }

    replaceDouBanImageHost(url) {
        if (url.indexOf('https://img2.') !== -1) {
            return url.replace('https://img2.', 'https://img9.')
        }
        return url
    }

    async getRecommendDataPage1(repData) {
        // 推荐
        const [remenPro, moviePro, tvPro, yingshiPro] = await Promise.all([
            req(
                'https://m.douban.com/rexxar/api/v2/movie/modules?need_manual_chart_card=1&for_mobile=1',
                {
                    headers: this.headers,
                    method: 'GET',
                }
            ),
            req(
                'https://m.douban.com/rexxar/api/v2/subject_collection/movie_real_time_hotest/items?type=movie&start=0&count=10&for_mobile=1',
                {
                    headers: this.headers,
                    method: 'GET',
                }
            ),
            req(
                'https://m.douban.com/rexxar/api/v2/subject_collection/tv_real_time_hotest/items?type=tv&start=0&count=10&for_mobile=1',
                {
                    headers: this.headers,
                    method: 'GET',
                }
            ),
            req('https://api.yingshi.tv/page/v4.5/typepage?id=0'),
        ])

        // Process remenPro data
        for (let index = 0; index < remenPro.data.modules.length; index++) {
            const element = remenPro.data.modules[index]
            if (
                element.data?.subject_collection_boards?.[0]?.subject_collection
                    ?.name === '豆瓣热门'
            ) {
                let ad = new RepAd()
                ad.title = '豆瓣热门'
                ad.uiType = UIType.smallCard
                let videoList =
                    element.data?.subject_collection_boards?.[0]?.items
                for (let index = 0; index < videoList.length; index++) {
                    const element = videoList[index]
                    let video = new VideoDetail()
                    video.vod_name = element.title
                    video.vod_pic = this.replaceDouBanImageHost(
                        element.cover?.url
                    )
                    video.vod_remarks = '豆瓣' + element.rating?.value
                    ad.data.push(video)
                }
                repData.data.push(ad)
                break
            }
        }

        // Process tvPro data
        let tvList = tvPro?.data?.subject_collection_items
        if (tvList?.length > 0) {
            let ad = new RepAd()
            ad.title = '豆瓣实时电视榜'
            ad.uiType = UIType.smallCard
            for (let index = 0; index < tvList.length; index++) {
                const element3 = tvList[index]
                let video = new VideoDetail()
                video.vod_name = element3.title
                video.vod_pic = this.replaceDouBanImageHost(element3.cover?.url)
                video.vod_remarks = '豆瓣' + element3.rating?.value
                ad.data.push(video)
            }
            repData.data.push(ad)
        }
        // Process moviePro data
        var movieList = moviePro?.data?.subject_collection_items
        if (movieList?.length > 0) {
            let ad = new RepAd()
            ad.title = '豆瓣实时电影榜'
            ad.uiType = UIType.smallCard
            for (let index = 0; index < movieList.length; index++) {
                const element3 = movieList[index]
                let video = new VideoDetail()
                video.vod_name = element3.title
                video.vod_pic = this.replaceDouBanImageHost(element3.cover?.url)
                video.vod_remarks = '豆瓣' + element3.rating?.value
                ad.data.push(video)
            }
            repData.data.push(ad)
        }

        let bannerData = yingshiPro?.data?.data?.carousel
        if (bannerData?.length > 0) {
            let bannerAD = new RepAd()
            bannerAD.uiType = UIType.banner
            bannerAD.ratio = 1920.0 / 772.0
            for (let index = 0; index < bannerData.length; index++) {
                const element3 = bannerData[index]
                let video = new VideoDetail()
                video.vod_name = element3.carousel_name
                video.vod_pic = element3.carousel_pic_pc
                // 替换,为 ｜
                video.vod_remarks = element3.carousel_vod_class?.replace(
                    /,/g,
                    '｜'
                )
                bannerAD.data.push(video)
            }
            // 插入第一位
            repData.data.splice(0, 0, bannerAD)
        }
        let jingXuanData = yingshiPro?.data?.data?.yunying?.[0]?.vod_list
        if (jingXuanData?.length > 0) {
            let jingXuanAD = new RepAd()
            jingXuanAD.title = '精选热播'
            jingXuanAD.uiType = UIType.largeCard
            for (let index = 0; index < jingXuanData.length; index++) {
                const element3 = jingXuanData[index]
                let video = new VideoDetail()
                video.vod_name = element3.vod_name
                video.vod_pic = element3.vod_pic
                jingXuanAD.data.push(video)
            }
            repData.data.push(jingXuanAD)
        }

        let otherList = yingshiPro?.data?.data?.categories
        if (otherList?.length > 0) {
            for (let index = 0; index < otherList.length; index++) {
                const element = otherList[index]
                let otherAD = new RepAd()
                otherAD.uiType = UIType.smallCard
                otherAD.title = element.type_name
                for (let index = 0; index < element.vod_list?.length; index++) {
                    const element2 = element.vod_list?.[index]
                    let video = new VideoDetail()
                    video.vod_name = element2.vod_name
                    video.vod_pic = element2.vod_pic
                    otherAD.data.push(video)
                }
                repData.data.push(otherAD)
            }
        }
    }

    async getMoreRecommendData(page, repData) {
        const response = await req(
            `https://api.yingshi.tv/topic/v1/list?limit=12&page=${page}`
        )
        let list = response?.data?.data?.List
        if (list?.length > 0) {
            for (let index = 0; index < list.length; index++) {
                const element = list[index]
                let ad = new RepAd()
                ad.uiType = UIType.smallCard
                ad.title = element.topic_name
                for (let index = 0; index < element.vod_list?.length; index++) {
                    const element2 = element.vod_list?.[index]
                    let video = new VideoDetail()
                    video.vod_name = element2.vod_name
                    video.vod_pic = element2.vod_pic
                    ad.data.push(video)
                }
                repData.data.push(ad)
            }
        }
    }

    async getTVFilterLabsData(repData) {
        // 找电视剧
        let title0 = new FilterTitle()
        title0.name = '类型'
        // prettier-ignore
        let labs0 = ["全部","喜剧","爱情","悬疑","动画","武侠","古装","家庭","犯罪","科幻","恐怖","历史","战争","动作","冒险","传记","剧情","奇幻","惊悚","灾难","歌舞","音乐"];
        for (let index = 0; index < labs0.length; index++) {
            const element = labs0[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = index === 0 ? '' : element
            lab.key = '类型'
            title0.list.push(lab)
        }
        repData.filter.push(title0)

        let title1 = new FilterTitle()
        title1.name = '地区'
        // prettier-ignore
        let labs1 = ["全部","华语","欧美","国外","韩国","日本","中国大陆","中国香港","美国","英国","泰国","中国台湾","意大利","法国","德国","西班牙","俄罗斯","瑞典","巴西","丹麦","印度","加拿大","爱尔兰","澳大利亚"];
        for (let index = 0; index < labs1.length; index++) {
            const element = labs1[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = element
            lab.key = '地区'
            if (index === 0) {
                lab.id = ''
            } else if (index === 1) {
                lab.key = ''
            }
            title1.list.push(lab)
        }
        repData.filter.push(title1)

        let title2 = new FilterTitle()
        title2.name = '年代'
        // prettier-ignore
        let labs2 = ["全部","2020年代","2024","2023","2022","2021","2020","2019","2010年代","2000年代","90年代","80年代","70年代","60年代","更早"];
        for (let index = 0; index < labs2.length; index++) {
            const element = labs2[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = element
            if (index === 0) {
                lab.id = ''
            }
            title2.list.push(lab)
        }
        repData.filter.push(title2)

        let title3 = new FilterTitle()
        title3.name = '平台'
        // prettier-ignore
        let labs3 = ["全部","腾讯视频","爱奇艺","优酷","湖南卫视","Netflix","HBO","BBC","NHK","CBS","NBC","tvN"];
        for (let index = 0; index < labs3.length; index++) {
            const element = labs3[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = element
            if (index === 0) {
                lab.id = ''
            }
            title3.list.push(lab)
        }
        repData.filter.push(title3)

        let title4 = new FilterTitle()
        title4.name = '排序'
        // prettier-ignore
        let labs4 = ["近期热度","综合排序","首播时间","高分优先"];
        let ids = ['U', '', 'R', 'S']
        for (let index = 0; index < labs4.length; index++) {
            const element = labs4[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = ids[index]
            title4.list.push(lab)
        }
        repData.filter.push(title4)
    }

    async getMovieFilterLabsData(repData) {
        // 找电视剧
        let title0 = new FilterTitle()
        title0.name = '类型'
        // prettier-ignore
        let labs0 = ["全部","喜剧","爱情","动作","科幻","动画","悬疑","犯罪","惊悚","冒险","音乐","历史","奇幻","恐怖","战争","传记","歌舞","武侠","情色","灾难","西部","纪录片","短片"];
        for (let index = 0; index < labs0.length; index++) {
            const element = labs0[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = index === 0 ? '' : element
            lab.key = '类型'
            title0.list.push(lab)
        }
        repData.filter.push(title0)

        let title1 = new FilterTitle()
        title1.name = '地区'
        // prettier-ignore
        let labs1 = ["全部","华语","欧美","韩国","日本","中国大陆","美国","中国香港","中国台湾","英国","法国","德国","意大利","西班牙","印度","泰国","俄罗斯","加拿大","澳大利亚","爱尔兰","瑞典","巴西","丹麦"];
        for (let index = 0; index < labs1.length; index++) {
            const element = labs1[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = element
            lab.key = '地区'
            if (index === 0) {
                lab.id = ''
            } else if (index === 1) {
                lab.key = ''
            }
            title1.list.push(lab)
        }
        repData.filter.push(title1)

        let title2 = new FilterTitle()
        title2.name = '年代'
        // prettier-ignore
        let labs2 = ["全部","2020年代","2024","2023","2022","2021","2020","2019","2010年代","2000年代","90年代","80年代","70年代","60年代","更早"];
        for (let index = 0; index < labs2.length; index++) {
            const element = labs2[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = element
            if (index === 0) {
                lab.id = ''
            }
            title2.list.push(lab)
        }
        repData.filter.push(title2)

        let title3 = new FilterTitle()
        title3.name = '平台'
        // prettier-ignore
        let labs3 = ["全部","腾讯视频","爱奇艺","优酷","湖南卫视","Netflix","HBO","BBC","NHK","CBS","NBC","tvN"];
        for (let index = 0; index < labs3.length; index++) {
            const element = labs3[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = element
            if (index === 0) {
                lab.id = ''
            }
            title3.list.push(lab)
        }
        repData.filter.push(title3)

        let title4 = new FilterTitle()
        title4.name = '排序'
        // prettier-ignore
        let labs4 = ["近期热度","综合排序","首播时间","高分优先"];
        let ids = ['U', '', 'R', 'S']
        for (let index = 0; index < labs4.length; index++) {
            const element = labs4[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = ids[index]
            title4.list.push(lab)
        }
        repData.filter.push(title4)
    }

    async getZYFilterLabsData(repData) {
        // 找电视剧
        let title0 = new FilterTitle()
        title0.name = '类型'
        // prettier-ignore
        let labs0 = ["全部","真人秀","脱口秀","音乐","歌舞"];
        for (let index = 0; index < labs0.length; index++) {
            const element = labs0[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = index === 0 ? '' : element
            lab.key = '类型'
            title0.list.push(lab)
        }
        repData.filter.push(title0)

        let title1 = new FilterTitle()
        title1.name = '地区'
        // prettier-ignore
        let labs1 = ["全部","华语","欧美","国外","韩国","日本","中国大陆","中国香港","美国","英国","泰国","中国台湾","意大利","法国","德国","西班牙","俄罗斯","瑞典","巴西","丹麦","印度","加拿大","爱尔兰","澳大利亚"];
        for (let index = 0; index < labs1.length; index++) {
            const element = labs1[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = element
            lab.key = '地区'
            if (index === 0) {
                lab.id = ''
            } else if (index === 1) {
                lab.key = ''
            }
            title1.list.push(lab)
        }
        repData.filter.push(title1)

        let title2 = new FilterTitle()
        title2.name = '年代'
        // prettier-ignore
        let labs2 = ["全部","2020年代","2024","2023","2022","2021","2020","2019","2010年代","2000年代","90年代","80年代","70年代","60年代","更早"];
        for (let index = 0; index < labs2.length; index++) {
            const element = labs2[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = element
            if (index === 0) {
                lab.id = ''
            }
            title2.list.push(lab)
        }
        repData.filter.push(title2)

        let title3 = new FilterTitle()
        title3.name = '平台'
        // prettier-ignore
        let labs3 = ["全部","腾讯视频","爱奇艺","优酷","湖南卫视","Netflix","HBO","BBC","NHK","CBS","NBC","tvN"];
        for (let index = 0; index < labs3.length; index++) {
            const element = labs3[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = element
            if (index === 0) {
                lab.id = ''
            }
            title3.list.push(lab)
        }
        repData.filter.push(title3)

        let title4 = new FilterTitle()
        title4.name = '排序'
        // prettier-ignore
        let labs4 = ["近期热度","综合排序","首播时间","高分优先"];
        let ids = ['U', '', 'R', 'S']
        for (let index = 0; index < labs4.length; index++) {
            const element = labs4[index]
            let lab = new FilterLabel()
            lab.name = element
            lab.id = ids[index]
            title4.list.push(lab)
        }
        repData.filter.push(title4)
    }

    /**
     * 获取筛选列表数据
     * 当 getTab() 返回 RepTabList.filter 时调用
     * @param {UZSubclassVideoListArgs} args 主要参数 args.mainClassId（即 VideoClass.id） 、 args.filter( 按 getTab() 返回的 filter 顺序传入)
     * @returns {Promise<RepVideoList>}返回筛选列表
     */
    async getFilterList(args) {
        let repData = new RepVideoList()
        try {
            let filterLabs = args.filter
            let page = args.page - 1
            if (args?.mainClassId === '1') {
                await this.getTVFilterList(filterLabs, repData, page)
            } else if (args?.mainClassId === '2') {
                await this.getMovieFilterList(filterLabs, repData, page)
            } else if (args?.mainClassId === '3') {
                await this.getZYFilterList(filterLabs, repData, page)
            }
        } catch (error) {
            repData.error = error.toString()
        }
        return JSON.stringify(repData)
    }

    async getTVFilterList(filterLabs, repData, page) {
        let param1 = '{"形式":"电视剧",'
        let tags = ''
        for (let index = 0; index < filterLabs.length - 1; index++) {
            const lab = filterLabs[index]

            if (lab.key !== '') {
                param1 += '"' + lab.key + '":"' + lab.id + '",'
            }
            if (lab.id !== '') {
                tags += lab.id + ','
            }
        }
        param1.endsWith(',') &&
            (param1 = param1.substring(0, param1.length - 1))
        param1 += '}'
        tags.endsWith(',') && (tags = tags.substring(0, tags.length - 1))
        let sort = filterLabs[filterLabs.length - 1].id

        let start = page * 20
        let url = `https://m.douban.com/rexxar/api/v2/tv/recommend?refresh=0&start=${start}&count=20&selected_categories=${param1}&uncollect=false&tags=${tags}&sort=${sort}`

        let response = await req(url, { headers: this.headers })
        let list = response?.data?.items ?? []
        for (let index = 0; index < list.length; index++) {
            const element = list[index]
            let video = new VideoDetail()
            video.vod_name = element.title
            video.vod_pic = this.replaceDouBanImageHost(
                element.pic?.normal ?? element.pic?.large ?? ''
            )
            video.vod_remarks = '豆瓣' + (element.rating?.value ?? '')
            repData.data.push(video)
        }
    }

    async getMovieFilterList(filterLabs, repData, page) {
        let param1 = '{'
        let tags = ''
        for (let index = 0; index < filterLabs.length - 1; index++) {
            const lab = filterLabs[index]

            if (lab.key !== '') {
                param1 += '"' + lab.key + '":"' + lab.id + '",'
            }
            if (lab.id !== '') {
                tags += lab.id + ','
            }
        }
        param1.endsWith(',') &&
            (param1 = param1.substring(0, param1.length - 1))
        param1 += '}'
        tags.endsWith(',') && (tags = tags.substring(0, tags.length - 1))
        let sort = filterLabs[filterLabs.length - 1].id

        let start = page * 20
        let url = `https://m.douban.com/rexxar/api/v2/movie/recommend??refresh=0&start=${start}&count=20&selected_categories=${param1}&uncollect=false&tags=${tags}&sort=${sort}`

        let response = await req(url, { headers: this.headers })
        let list = response?.data?.items ?? []
        for (let index = 0; index < list.length; index++) {
            const element = list[index]
            let video = new VideoDetail()
            video.vod_name = element.title
            video.vod_pic = this.replaceDouBanImageHost(
                element.pic?.normal ?? element.pic?.large ?? ''
            )
            video.vod_remarks = '豆瓣' + (element.rating?.value ?? '')
            repData.data.push(video)
        }
    }

    async getZYFilterList(filterLabs, repData, page) {
        let param1 = '{"形式":"综艺",'
        let tags = '综艺,'
        for (let index = 0; index < filterLabs.length - 1; index++) {
            const lab = filterLabs[index]

            if (lab.key !== '') {
                param1 += '"' + lab.key + '":"' + lab.id + '",'
            }
            if (lab.id !== '') {
                tags += lab.id + ','
            }
        }
        param1.endsWith(',') &&
            (param1 = param1.substring(0, param1.length - 1))
        param1 += '}'
        tags.endsWith(',') && (tags = tags.substring(0, tags.length - 1))
        let sort = filterLabs[filterLabs.length - 1].id

        let start = page * 20
        let url = `https://m.douban.com/rexxar/api/v2/tv/recommend?refresh=0&start=${start}&count=20&selected_categories=${param1}&uncollect=false&tags=${tags}&sort=${sort}`

        let response = await req(url, { headers: this.headers })
        let list = response?.data?.items ?? []
        for (let index = 0; index < list.length; index++) {
            const element = list[index]
            let video = new VideoDetail()
            video.vod_name = element.title
            video.vod_pic = this.replaceDouBanImageHost(
                element.pic?.normal ?? element.pic?.large ?? ''
            )
            video.vod_remarks = '豆瓣' + (element.rating?.value ?? '')
            repData.data.push(video)
        }
    }
}

// 固定为 uzHomeJs
const uzHomeJs = new HomeJS()
