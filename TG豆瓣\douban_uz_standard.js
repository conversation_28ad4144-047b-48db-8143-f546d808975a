// ignore

//@name:🟢 豆瓣标准版
//@webSite:https://frodo.douban.com
//@version:1
//@remark:🍃豆儿出品，标准版移植！支持完整API和签名
//@codeID:
//@order: A

// ignore

// UZ平台豆瓣标准版移植
// 基于原标准版douban.js的完整功能移植

// 由于UZ不支持导入，直接定义所需的类
class VideoDetail {
    constructor() {
        this.vod_id = ''
        this.vod_name = ''
        this.vod_play_from = ''
        this.vod_play_url = ''
        this.vod_pic = ''
        this.type_name = ''
        this.vod_remarks = ''
        this.vod_douban_score = ''
        this.vod_lang = ''
        this.vod_year = ''
        this.vod_actor = ''
        this.vod_director = ''
        this.vod_content = ''
        this.vod_area = ''
        this.panUrls = []
    }
}

class VideoClass {
    constructor() {
        this.type_id = ''
        this.type_name = ''
        this.hasSubclass = false
    }
}

class FilterLabel {
    constructor() {
        this.name = ''
        this.id = ''
        this.key = ''
    }
}

class FilterTitle {
    constructor() {
        this.name = ''
        this.list = []
    }
}

class RepVideoClassList {
    constructor() {
        this.error = ''
        this.data = []
    }
}

class RepVideoSubclassList {
    constructor() {
        this.error = ''
        this.data = {
            filter: []
        }
    }
}

class RepVideoList {
    constructor() {
        this.error = ''
        this.data = []
        this.total = 0
    }
}

class RepVideoDetail {
    constructor() {
        this.error = ''
        this.data = null
    }
}

class RepVideoPlayUrl {
    constructor() {
        this.error = ''
        this.data = []
    }
}

const appConfig = {
    _webSite: 'https://frodo.douban.com',
    get webSite() {
        return this._webSite
    },
    set webSite(value) {
        this._webSite = value
    },

    _uzTag: '',
    get uzTag() {
        return this._uzTag
    },
    set uzTag(value) {
        this._uzTag = value
    },
}

// 豆瓣API域名
const domain = 'https://frodo.douban.com'

// 设备信息管理
let device = {}

// 生成随机字符串
function generateRandomId(length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
}

// 获取Unix时间戳
function getUnixTimestamp() {
    return Math.floor(Date.now() / 1000)
}

// 初始化设备信息
function initDevice() {
    const deviceKey = 'douban_device_info'
    device = UZUtils.getStorage(deviceKey) || {}
    
    if (!device.id) {
        device.id = generateRandomId(40).toLowerCase()
        device.ua = `Rexxar-Core/0.1.3 api-client/1 com.douban.frodo/7.9.0(216) Android/28 product/Xiaomi11 rom/android network/wifi udid/${device.id} platform/mobile com.douban.frodo/7.9.0(216) Rexxar/1.2.151 platform/mobile 1.2.151`
        UZUtils.setStorage(deviceKey, device)
    }
    
    return device
}

// 豆瓣API签名函数
function sig(link) {
    const CryptoJS = createCryptoJS()
    
    link += `&udid=${device.id}&uuid=${device.id}&&rom=android&apikey=0dad551ec0f84ed02907ff5c42e8ec70&s=rexxar_new&channel=Yingyongbao_Market&timezone=Asia/Shanghai&device_id=${device.id}&os_rom=android&apple=c52fbb99b908be4d026954cc4374f16d&mooncake=0f607264fc6318a92b9e13c65db7cd3c&sugar=0`
    
    // 手动解析URL路径，避免使用不支持的URL构造函数
    const urlParts = link.split('/')
    const pathname = '/' + urlParts.slice(3).join('/')
    const ts = getUnixTimestamp().toString()
    let sha1 = CryptoJS.HmacSHA1('GET&' + encodeURIComponent(pathname) + '&' + ts, 'bf7dddc7c9cfe6f7')
    let signa = CryptoJS.enc.Base64.stringify(sha1)
    
    return link + '&_sig=' + encodeURIComponent(signa) + '&_ts=' + ts
}

// 网络请求封装
async function request(reqUrl, ua) {
    try {
        const response = await req(reqUrl, {
            headers: {
                'User-Agent': ua || device.ua,
            },
        })
        
        if (response.code === 200) {
            return response.data
        } else {
            throw new Error(`HTTP ${response.code}`)
        }
    } catch (error) {
        throw new Error(`请求失败: ${error.message}`)
    }
}

// 用于控制欢迎提示只显示一次
let hasShownWelcome = false

/**
 * 获取分类列表
 */
async function getClassList() {
    var backData = new RepVideoClassList()
    try {
        // 初始化设备信息
        initDevice()
        
        // 首次加载时显示欢迎提示
        if (!hasShownWelcome) {
            hasShownWelcome = true
            toast("🍃豆儿出品，标准版移植！", 3)
        }

        // 获取标签数据用于构建筛选
        const link = sig(domain + '/api/v2/movie/tag?sort=U&start=0&count=30&q=全部形式,全部类型,全部地区,全部年代&score_rang=0,10')
        const data = await request(link)
        
        let classes = [
            {
                type_id: 't1',
                type_name: '热播',
                hasSubclass: true
            },
            {
                type_id: 't2',
                type_name: '片库',
                hasSubclass: true
            },
            {
                type_id: 't250',
                type_name: 'Top250',
                hasSubclass: false
            },
            {
                type_id: 't3',
                type_name: '榜单',
                hasSubclass: false
            },
            {
                type_id: 't4',
                type_name: '片单',
                hasSubclass: true
            },
        ]

        backData.data = classes.map(item => {
            const videoClass = new VideoClass()
            videoClass.type_id = item.type_id
            videoClass.type_name = item.type_name
            videoClass.hasSubclass = item.hasSubclass
            return videoClass
        })

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取二级分类列表筛选列表
 */
async function getSubclassList(args) {
    var backData = new RepVideoSubclassList()
    try {
        const typeId = args.url
        
        if (typeId === 't1') {
            // 热播分类的筛选
            const filterTitle = new FilterTitle()
            filterTitle.name = '类型'
            
            const options = [
                { name: '电影', id: 'movie/hot_gaia' },
                { name: '电视剧', id: 'subject_collection/tv_hot/items' },
                { name: '国产剧', id: 'subject_collection/tv_domestic/items' },
                { name: '美剧', id: 'subject_collection/tv_american/items' },
                { name: '日剧', id: 'subject_collection/tv_japanese/items' },
                { name: '韩剧', id: 'subject_collection/tv_korean/items' },
                { name: '动漫', id: 'subject_collection/tv_animation/items' },
                { name: '综艺', id: 'subject_collection/show_hot/items' },
            ]
            
            filterTitle.list = options.map(opt => {
                const label = new FilterLabel()
                label.name = opt.name
                label.id = opt.id
                label.key = 'u'
                return label
            })
            
            backData.data.filter = [filterTitle]
        } else if (typeId === 't2') {
            // 片库的筛选 - 需要获取豆瓣的标签数据
            const link = sig(domain + '/api/v2/movie/tag?sort=U&start=0&count=30&q=全部形式,全部类型,全部地区,全部年代&score_rang=0,10')
            const data = await request(link)
            
            let filterAll = []
            for (const tag of data.tags) {
                if (tag.type == '特色') continue
                
                const filterTitle = new FilterTitle()
                filterTitle.name = tag.type
                
                let fValues = []
                if (tag.type == '年代' && tag.data.indexOf(new Date().getFullYear().toString()) < 0) {
                    tag.data.splice(1, 0, new Date().getFullYear().toString())
                    if (tag.data.indexOf((new Date().getFullYear() - 1).toString()) < 0) {
                        tag.data.splice(2, 0, (new Date().getFullYear() - 1).toString())
                    }
                }
                
                for (const v of tag.data) {
                    const label = new FilterLabel()
                    label.name = v.indexOf('全部') >= 0 ? '全部' : v
                    label.id = v
                    label.key = tag.type
                    fValues.push(label)
                }
                
                filterTitle.list = fValues
                filterAll.push(filterTitle)
            }
            
            // 添加排序选项
            const sortFilter = new FilterTitle()
            sortFilter.name = '排序'
            sortFilter.list = data.sorts.map(sort => {
                const label = new FilterLabel()
                label.name = sort.text
                label.id = sort.name
                label.key = 'sort'
                return label
            })
            filterAll.push(sortFilter)
            
            backData.data.filter = filterAll
        } else if (typeId === 't4') {
            // 片单的筛选
            const filters = [
                {
                    name: '类型',
                    key: 'type',
                    options: [
                        { name: '全部', id: '' },
                        { name: '电影', id: 'movie' },
                        { name: '电视剧', id: 'tv' },
                    ]
                },
                {
                    name: '分类',
                    key: 'cate',
                    options: [
                        { name: '全部', id: 'all' },
                        { name: '豆瓣片单', id: 'official' },
                        { name: '精选', id: 'selected' },
                        { name: '经典', id: 'classical' },
                        { name: '获奖', id: 'prize' },
                        { name: '高分', id: 'high_score' },
                        { name: '榜单', id: 'movie_list' },
                        { name: '冷门佳片', id: 'dark_horse' },
                        { name: '主题', id: 'topic' },
                        { name: '导演', id: 'director' },
                        { name: '演员', id: 'actor' },
                        { name: '系列', id: 'series' },
                    ]
                }
            ]
            
            backData.data.filter = filters.map(filter => {
                const filterTitle = new FilterTitle()
                filterTitle.name = filter.name
                filterTitle.list = filter.options.map(opt => {
                    const label = new FilterLabel()
                    label.name = opt.name
                    label.id = opt.id
                    label.key = filter.key
                    return label
                })
                return filterTitle
            })
        }
        
    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取分类视频列表
 */
async function getVideoList(args) {
    var backData = new RepVideoList()
    try {
        const typeId = args.url
        const page = args.page || 1

        if (typeId === 't250') {
            // Top250
            const link = sig(`${domain}/api/v2/subject_collection/movie_top250/items?area=全部&sort=recommend&playable=0&loc_id=0&start=${(page - 1) * 30}&count=30`)
            const data = await request(link)

            let videos = []
            for (const vod of data.items || data.subject_collection_items) {
                const videoDetail = new VideoDetail()
                videoDetail.vod_id = vod.id
                videoDetail.vod_name = vod.title
                videoDetail.vod_pic = vod.pic.normal || vod.pic.large

                let score = (vod.rating ? vod.rating.value || '' : '').toString()
                videoDetail.vod_remarks = score.length > 0 ? '评分:' + score : ''
                videoDetail.vod_douban_score = score
                videoDetail.type_name = '电影'

                videos.push(videoDetail)
            }

            backData.data = videos
            backData.total = Math.ceil(data.total / 30)
        } else if (typeId === 't3') {
            // 榜单
            let videos = []

            // 电影榜单
            let link = sig(`${domain}/api/v2/movie/category_ranks?count=30&category=recent_hot`)
            let data = await request(link)

            for (const vod of data.selected_collections) {
                const videoDetail = new VideoDetail()
                videoDetail.vod_id = 'cr_' + vod.id
                videoDetail.vod_name = vod.short_name || vod.title
                videoDetail.vod_pic = vod.cover_url
                videoDetail.vod_remarks = '榜单'
                videoDetail.type_name = '电影榜单'
                videos.push(videoDetail)
            }

            // 电视剧榜单
            link = sig(`${domain}/api/v2/tv/category_ranks?count=30&category=recent_hot`)
            data = await request(link)

            for (const vod of data.selected_collections) {
                const videoDetail = new VideoDetail()
                videoDetail.vod_id = 'cr_' + vod.id
                videoDetail.vod_name = vod.short_name || vod.title
                videoDetail.vod_pic = vod.cover_url
                videoDetail.vod_remarks = '榜单'
                videoDetail.type_name = '电视剧榜单'
                videos.push(videoDetail)
            }

            backData.data = videos
            backData.total = 1
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取二级分类视频列表
 */
async function getSubclassVideoList(args) {
    var backData = new RepVideoList()
    try {
        const typeId = args.mainClassId
        const page = args.page || 1
        const filters = args.filter || {}

        if (typeId === 't1') {
            // 热播内容
            const u = filters.u || 'movie/hot_gaia'
            const link = sig(`${domain}/api/v2/${u}?area=全部&sort=recommend&playable=0&loc_id=0&start=${(page - 1) * 30}&count=30`)
            const data = await request(link)

            let videos = []
            for (const vod of data.items || data.subject_collection_items) {
                const videoDetail = new VideoDetail()
                videoDetail.vod_id = vod.id
                videoDetail.vod_name = vod.title
                videoDetail.vod_pic = vod.pic.normal || vod.pic.large

                let score = (vod.rating ? vod.rating.value || '' : '').toString()
                videoDetail.vod_remarks = score.length > 0 ? '评分:' + score : ''
                videoDetail.vod_douban_score = score
                videoDetail.type_name = u.includes('movie') ? '电影' : '电视剧'

                videos.push(videoDetail)
            }

            backData.data = videos
            backData.total = Math.ceil(data.total / 30)
        } else if (typeId === 't2') {
            // 片库筛选
            const sort = filters.sort || 'U'
            const form = filters['形式'] || '全部形式'
            const type = filters['类型'] || '全部类型'
            const area = filters['地区'] || '全部地区'
            const year = filters['年代'] || '全部年代'

            const link = sig(`${domain}/api/v2/movie/tag?sort=${sort}&start=${(page - 1) * 30}&count=30&q=${form},${type},${area},${year}&score_rang=0,10`)
            const data = await request(link)

            let videos = []
            for (const vod of data.data) {
                const videoDetail = new VideoDetail()
                videoDetail.vod_id = vod.id
                videoDetail.vod_name = vod.title
                videoDetail.vod_pic = vod.pic.normal || vod.pic.large

                let score = (vod.rating ? vod.rating.value || '' : '').toString()
                videoDetail.vod_remarks = score.length > 0 ? '评分:' + score : ''
                videoDetail.vod_douban_score = score
                videoDetail.type_name = '电影'

                videos.push(videoDetail)
            }

            backData.data = videos
            backData.total = Math.ceil(data.total / 30)
        } else if (typeId === 't4') {
            // 片单
            const type = filters.type || ''
            const cate = filters.cate || 'all'

            const link = sig(`${domain}/api/v2/skynet/new_playlists?subject_type=${type}&category=${cate}&loc_id=0&start=${(page - 1) * 30}&count=30`)
            const data = await request(link)

            let videos = []
            for (const vod of data.data[0].items) {
                const videoDetail = new VideoDetail()
                videoDetail.vod_id = vod.owner ? 'dl_' + vod.id : 'cr_' + vod.id
                videoDetail.vod_name = vod.title
                videoDetail.vod_pic = vod.cover_url
                videoDetail.vod_remarks = '片单'
                videoDetail.type_name = '片单'
                videos.push(videoDetail)
            }

            backData.data = videos
            backData.total = Math.ceil(data.total / 30)
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取视频详情
 */
async function getVideoDetail(args) {
    var backData = new RepVideoDetail()
    try {
        const vodId = args.url

        // 处理特殊ID（榜单和片单）
        if (vodId.startsWith('cr_')) {
            // 榜单内容
            const collectionId = vodId.substring(3)
            const link = sig(`${domain}/api/v2/subject_collection/${collectionId}/items?start=0&count=30&updated_at=&items_only=1`)
            const data = await request(link)

            const videoDetail = new VideoDetail()
            videoDetail.vod_id = vodId
            videoDetail.vod_name = '豆瓣榜单内容'
            videoDetail.vod_content = '这是豆瓣榜单的内容列表，仅供浏览，不提供播放功能。'
            videoDetail.vod_remarks = '榜单内容'

            // 将榜单内容作为剧集列表
            let episodes = []
            for (let i = 0; i < data.subject_collection_items.length; i++) {
                const item = data.subject_collection_items[i]
                episodes.push(`${item.title}$${item.id}`)
            }
            videoDetail.vod_play_url = episodes.join('#')
            videoDetail.vod_play_from = '豆瓣榜单'

            backData.data = videoDetail
        } else if (vodId.startsWith('dl_')) {
            // 片单内容
            const doulistId = vodId.substring(3)
            const link = sig(`${domain}/api/v2/doulist/${doulistId}/posts?start=0&count=30&updated_at=&items_only=1`)
            const data = await request(link)

            const videoDetail = new VideoDetail()
            videoDetail.vod_id = vodId
            videoDetail.vod_name = '豆瓣片单内容'
            videoDetail.vod_content = '这是豆瓣片单的内容列表，仅供浏览，不提供播放功能。'
            videoDetail.vod_remarks = '片单内容'

            // 将片单内容作为剧集列表
            let episodes = []
            for (let i = 0; i < data.items.length; i++) {
                const item = data.items[i]
                if (item.content && item.content.subject) {
                    const subject = item.content.subject
                    episodes.push(`${subject.title}$${subject.id}`)
                }
            }
            videoDetail.vod_play_url = episodes.join('#')
            videoDetail.vod_play_from = '豆瓣片单'

            backData.data = videoDetail
        } else {
            // 普通视频详情
            const videoDetail = new VideoDetail()
            videoDetail.vod_id = vodId
            videoDetail.vod_name = '豆瓣影视详情'
            videoDetail.vod_content = '这是来自豆瓣的影视信息，仅供参考，不提供播放功能。'
            videoDetail.vod_remarks = '仅展示信息'

            backData.data = videoDetail
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取视频播放地址
 */
async function getVideoPlayUrl(args) {
    var backData = new RepVideoPlayUrl()
    backData.error = '豆瓣仅提供影视信息展示，不提供播放功能'
    return JSON.stringify(backData)
}

/**
 * 搜索视频
 */
async function searchVideo(args) {
    var backData = new RepVideoList()
    try {
        const keyword = args.searchWord
        const page = args.page || 1

        if (!keyword) {
            backData.error = '请输入搜索关键词'
            return JSON.stringify(backData)
        }

        // 使用豆瓣搜索API
        const link = sig(`${domain}/api/v2/search/movie?q=${encodeURIComponent(keyword)}&start=${(page - 1) * 30}&count=30`)
        const data = await request(link)

        let videos = []
        for (const vod of data.items) {
            const videoDetail = new VideoDetail()
            videoDetail.vod_id = vod.id
            videoDetail.vod_name = vod.title
            videoDetail.vod_pic = vod.pic ? (vod.pic.normal || vod.pic.large) : ''

            let score = (vod.rating ? vod.rating.value || '' : '').toString()
            videoDetail.vod_remarks = score.length > 0 ? '评分:' + score : ''
            videoDetail.vod_douban_score = score
            videoDetail.type_name = vod.subtype === 'movie' ? '电影' : '电视剧'

            videos.push(videoDetail)
        }

        backData.data = videos
        backData.total = Math.ceil(data.total / 30)

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}
