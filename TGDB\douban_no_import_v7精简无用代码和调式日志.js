// ignore

//@name:✨豆瓣
//@webSite:https://movie.douban.com
//@version:1
//@remark:🍃豆儿出品，不属精品！
//@codeID:
//@order: A

// ignore

// 由于uz不支持导入，直接定义所需的类

class VideoDetail {
    constructor() {
        this.vod_id = ''
        this.vod_name = ''
        this.vod_play_from = ''
        this.vod_play_url = ''
        this.vod_pic = ''
        this.type_name = ''
        this.vod_remarks = ''
        this.vod_douban_score = ''
        this.vod_lang = ''
        this.vod_year = ''
        this.vod_actor = ''
        this.vod_director = ''
        this.vod_content = ''
        this.vod_area = ''
        this.panUrls = []
    }
}

class VideoClass {
    constructor() {
        this.type_id = ''
        this.type_name = ''
        this.hasSubclass = false
    }
}

class FilterLabel {
    constructor() {
        this.name = ''
        this.id = ''
        this.key = ''
    }
}

class FilterTitle {
    constructor() {
        this.name = ''
        this.list = []
    }
}

class RepVideoClassList {
    constructor() {
        this.error = ''
        this.data = []
    }
}

class RepVideoSubclassList {
    constructor() {
        this.error = ''
        this.data = {
            filter: []
        }
    }
}

class RepVideoList {
    constructor() {
        this.error = ''
        this.data = []
        this.total = 0
    }
}

class RepVideoDetail {
    constructor() {
        this.error = ''
        this.data = null
    }
}

class RepVideoPlayUrl {
    constructor() {
        this.error = ''
        this.data = []
    }
}

const appConfig = {
    _webSite: 'https://movie.douban.com',
    get webSite() {
        return this._webSite
    },
    set webSite(value) {
        this._webSite = value
    },

    _uzTag: '',
    get uzTag() {
        return this._uzTag
    },
    set uzTag(value) {
        this._uzTag = value
    },
}

// 用于控制欢迎提示只显示一次
let hasShownWelcome = false

/**
 * 获取分类列表
 */
async function getClassList() {
    var backData = new RepVideoClassList()
    try {
        // 首次加载时显示欢迎提示
        if (!hasShownWelcome) {
            hasShownWelcome = true
            toast("🍃豆儿出品，不属精品！", 3)
        }

        const classes = [
            { type_id: 'tv_hot', type_name: '热门剧集', hasSubclass: false },
            { type_id: 'movie_hot', type_name: '热门电影', hasSubclass: false },
            { type_id: 'movie_top250', type_name: 'Top250', hasSubclass: false },
            { type_id: 'tv_domestic', type_name: '国产剧', hasSubclass: false },
            { type_id: 'tv_variety', type_name: '综艺', hasSubclass: false },
            { type_id: 'tv_documentary', type_name: '纪录', hasSubclass: false },
            { type_id: 'tv_anime', type_name: '日漫', hasSubclass: false },
            { type_id: 'tv_hongkong', type_name: '港剧', hasSubclass: false },
            { type_id: 'tv_japanese', type_name: '日剧', hasSubclass: false },
            { type_id: 'tv_korean', type_name: '韩剧', hasSubclass: false },
            { type_id: 'tv_american', type_name: '美剧', hasSubclass: false },
            { type_id: 'tv_british', type_name: '英剧', hasSubclass: false }
        ]

        backData.data = classes.map(item => {
            const videoClass = new VideoClass()
            videoClass.type_id = item.type_id
            videoClass.type_name = item.type_name
            videoClass.hasSubclass = item.hasSubclass
            return videoClass
        })

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取二级分类列表筛选列表
 */
async function getSubclassList() {
    var backData = new RepVideoSubclassList()
    return JSON.stringify(backData)
}

/**
 * 获取分类视频列表
 */
async function getVideoList(args) {
    var backData = new RepVideoList()
    try {
        const typeId = args.url
        const page = args.page || 1
        const pageStart = (page - 1) * 25

        let type = 'movie'
        let tag = '热门'

        // 根据分类ID设置类型和标签
        if (typeId === 'tv_hot') {
            type = 'tv'
            tag = '热门'
        } else if (typeId === 'movie_top250') {
            return await getTop250Movies(page, backData)
        } else if (typeId === 'tv_domestic') {
            type = 'tv'
            tag = '国产剧'
        } else if (typeId === 'tv_variety') {
            type = 'tv'
            tag = '综艺'
        } else if (typeId === 'tv_documentary') {
            type = 'tv'
            tag = '纪录片'
        } else if (typeId === 'tv_anime') {
            type = 'tv'
            tag = '日本动画'
        } else if (typeId === 'tv_hongkong') {
            type = 'tv'
            tag = '港剧'
        } else if (typeId === 'tv_japanese') {
            type = 'tv'
            tag = '日剧'
        } else if (typeId === 'tv_korean') {
            type = 'tv'
            tag = '韩剧'
        } else if (typeId === 'tv_american') {
            type = 'tv'
            tag = '美剧'
        } else if (typeId === 'tv_british') {
            type = 'tv'
            tag = '英剧'
        }

        const doubanUrl = `https://movie.douban.com/j/search_subjects?type=${type}&tag=${tag}&sort=recommend&page_limit=25&page_start=${pageStart}`

        const response = await req(doubanUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://movie.douban.com/',
                'Accept': 'application/json, text/plain, */*',
            }
        })

        if (response.code === 200) {
            let data
            if (typeof response.data === 'object') {
                data = response.data
            } else if (typeof response.data === 'string') {
                try {
                    data = JSON.parse(response.data)
                } catch (parseError) {
                    backData.error = 'JSON解析失败'
                    return JSON.stringify(backData)
                }
            } else {
                backData.error = '未知的响应数据类型'
                return JSON.stringify(backData)
            }

            if (data.subjects && Array.isArray(data.subjects)) {
                const videoList = []
                for (let i = 0; i < data.subjects.length; i++) {
                    try {
                        const item = data.subjects[i]
                        const videoDetail = new VideoDetail()
                        videoDetail.vod_id = 'douban_' + (item.title || 'unknown') + '_' + (item.id || i)
                        videoDetail.vod_name = item.title || ''

                        if (item.cover && typeof item.cover === 'string') {
                            videoDetail.vod_pic = item.cover.replace(/^http:/, 'https:')
                        }

                        videoDetail.vod_douban_score = item.rate || ''
                        videoDetail.vod_remarks = item.rate ? '⭐ ' + item.rate : '⭐ 暂无评分'
                        videoDetail.type_name = type === 'movie' ? '电影' : '电视剧'
                        videoDetail.vod_content = '豆瓣评分：' + (item.rate || '暂无评分')

                        videoList.push(videoDetail)
                    } catch (itemError) {
                        // 忽略单个条目错误，继续处理其他条目
                    }
                }

                backData.data = videoList
                backData.total = videoList.length
            } else {
                backData.error = '豆瓣API返回数据格式异常'
            }
        } else {
            backData.error = '获取豆瓣数据失败'
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取二级分类视频列表
 */
async function getSubclassVideoList(args) {
    // 直接调用 getVideoList，避免代码重复
    return await getVideoList({ url: args.mainClassId, page: args.page })
}

/**
 * 获取视频详情
 */
async function getVideoDetail(args) {
    var backData = new RepVideoDetail()
    try {
        const videoDetail = new VideoDetail()
        videoDetail.vod_id = args.url
        videoDetail.vod_name = '豆瓣影视详情'
        videoDetail.vod_content = '这是来自豆瓣的影视信息，仅供参考，不提供播放功能。'
        videoDetail.vod_remarks = '仅展示信息'
        backData.data = videoDetail
    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取Top250电影列表
 */
async function getTop250Movies(page, backData) {
    try {
        const start = (page - 1) * 25
        const url = `https://movie.douban.com/top250?start=${start}&filter=`

        const response = await req(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://movie.douban.com/'
            }
        })

        if (response.code === 200) {
            const html = response.data
            const movies = parseTop250Html(html)
            backData.data = movies
            backData.total = movies.length
        } else {
            backData.error = 'Top250页面获取失败'
        }

    } catch (error) {
        backData.error = error.toString()
    }

    return JSON.stringify(backData)
}

/**
 * 解析Top250 HTML页面
 */
function parseTop250Html(html) {
    const movies = []

    try {
        const itemRegex = /<div class="item">[\s\S]*?<\/div>\s*<\/div>\s*<\/div>/g
        const items = html.match(itemRegex) || []

        for (let i = 0; i < items.length; i++) {
            const item = items[i]

            const titleMatch = item.match(/<span class="title">([^<]+)<\/span>/)
            const title = titleMatch ? titleMatch[1] : ''

            const ratingMatch = item.match(/<span class="rating_num"[^>]*>([^<]+)<\/span>/)
            const rating = ratingMatch ? ratingMatch[1] : ''

            const picMatch = item.match(/<img[^>]+src="([^"]+)"/)
            const pic = picMatch ? picMatch[1] : ''

            const idMatch = item.match(/subject\/(\d+)\//)
            const doubanId = idMatch ? idMatch[1] : ''

            if (title) {
                const videoDetail = new VideoDetail()
                videoDetail.vod_id = 'douban_top250_' + doubanId + '_' + i
                videoDetail.vod_name = title
                videoDetail.vod_pic = pic.replace(/^http:/, 'https:')
                videoDetail.vod_douban_score = rating
                videoDetail.vod_remarks = rating ? '⭐ ' + rating : '⭐ 暂无评分'
                videoDetail.type_name = '电影'
                videoDetail.vod_content = '豆瓣Top250 评分：' + rating

                movies.push(videoDetail)
            }
        }

    } catch (error) {
        // 解析失败时返回空数组
    }

    return movies
}

/**
 * 获取视频播放地址
 */
async function getVideoPlayUrl() {
    var backData = new RepVideoPlayUrl()
    backData.error = '豆瓣仅提供影视信息展示，不提供播放功能'
    return JSON.stringify(backData)
}

/**
 * 搜索视频
 */
async function searchVideo() {
    var backData = new RepVideoList()
    backData.error = '搜索功能开发中'
    return JSON.stringify(backData)
}
