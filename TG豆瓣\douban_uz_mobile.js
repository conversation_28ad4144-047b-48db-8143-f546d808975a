// ignore

//@name:📱 豆瓣移动版
//@webSite:https://m.douban.com
//@version:1
//@remark:🍃豆儿出品，基于移动端API实现筛选功能！
//@codeID:
//@order: A

// ignore

// UZ平台豆瓣移动版
// 基于豆瓣移动端API实现，无需签名验证

// 由于UZ不支持导入，直接定义所需的类
class VideoDetail {
    constructor() {
        this.vod_id = ''
        this.vod_name = ''
        this.vod_play_from = ''
        this.vod_play_url = ''
        this.vod_pic = ''
        this.type_name = ''
        this.vod_remarks = ''
        this.vod_douban_score = ''
        this.vod_lang = ''
        this.vod_year = ''
        this.vod_actor = ''
        this.vod_director = ''
        this.vod_content = ''
        this.vod_area = ''
        this.panUrls = []
    }
}

class VideoClass {
    constructor() {
        this.type_id = ''
        this.type_name = ''
        this.hasSubclass = false
    }
}

class FilterLabel {
    constructor() {
        this.name = ''
        this.id = ''
        this.key = ''
    }
}

class FilterTitle {
    constructor() {
        this.name = ''
        this.list = []
    }
}

class RepVideoClassList {
    constructor() {
        this.error = ''
        this.data = []
    }
}

class RepVideoSubclassList {
    constructor() {
        this.error = ''
        this.data = {
            filter: []
        }
    }
}

class RepVideoList {
    constructor() {
        this.error = ''
        this.data = []
        this.total = 0
    }
}

class RepVideoDetail {
    constructor() {
        this.error = ''
        this.data = null
    }
}

class RepVideoPlayUrl {
    constructor() {
        this.error = ''
        this.data = []
    }
}

// 豆瓣移动端API域名
const mobileApiDomain = 'https://m.douban.com/rexxar/api/v2'

// 移动端请求头
const mobileHeaders = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Referer': 'https://m.douban.com/',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15'
}

// 用于控制欢迎提示只显示一次
let hasShownWelcome = false

// 图片链接优化
function replaceDouBanImageHost(url) {
    if (!url) return ''
    if (url.indexOf('https://img2.') !== -1) {
        return url.replace('https://img2.', 'https://img9.')
    }
    return url.replace(/^http:/, 'https:')
}

/**
 * 获取分类列表
 */
async function getClassList() {
    var backData = new RepVideoClassList()
    try {
        // 首次加载时显示欢迎提示
        if (!hasShownWelcome) {
            hasShownWelcome = true
            toast("🍃豆儿出品，移动版！", 3)
        }

        const classes = [
            {
                type_id: '1',
                type_name: '找电视剧',
                hasSubclass: true
            },
            {
                type_id: '2',
                type_name: '找电影',
                hasSubclass: true
            },
            {
                type_id: '3',
                type_name: '找综艺',
                hasSubclass: true
            },
            {
                type_id: 'hot_tv',
                type_name: '热门电视剧',
                hasSubclass: false
            },
            {
                type_id: 'hot_movie',
                type_name: '热门电影',
                hasSubclass: false
            }
        ]

        backData.data = classes.map(item => {
            const videoClass = new VideoClass()
            videoClass.type_id = item.type_id
            videoClass.type_name = item.type_name
            videoClass.hasSubclass = item.hasSubclass
            return videoClass
        })

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取二级分类列表筛选列表
 */
async function getSubclassList(args) {
    var backData = new RepVideoSubclassList()
    try {
        const typeId = args.url
        
        if (typeId === '1') {
            // 找电视剧的筛选
            await getTVFilterLabsData(backData)
        } else if (typeId === '2') {
            // 找电影的筛选
            await getMovieFilterLabsData(backData)
        } else if (typeId === '3') {
            // 找综艺的筛选
            await getZYFilterLabsData(backData)
        }
        
    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

async function getTVFilterLabsData(backData) {
    // 电视剧筛选
    let title0 = new FilterTitle()
    title0.name = '类型'
    let labs0 = ["全部","喜剧","爱情","悬疑","动画","武侠","古装","家庭","犯罪","科幻","恐怖","历史","战争","动作","冒险","传记","剧情","奇幻","惊悚","灾难","歌舞","音乐"]
    for (let index = 0; index < labs0.length; index++) {
        const element = labs0[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = index === 0 ? '' : element
        lab.key = '类型'
        title0.list.push(lab)
    }
    backData.data.filter.push(title0)

    let title1 = new FilterTitle()
    title1.name = '地区'
    let labs1 = ["全部","华语","欧美","国外","韩国","日本","中国大陆","中国香港","美国","英国","泰国","中国台湾","意大利","法国","德国","西班牙","俄罗斯","瑞典","巴西","丹麦","印度","加拿大","爱尔兰","澳大利亚"]
    for (let index = 0; index < labs1.length; index++) {
        const element = labs1[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = element
        lab.key = '地区'
        if (index === 0) {
            lab.id = ''
        } else if (index === 1) {
            lab.key = ''
        }
        title1.list.push(lab)
    }
    backData.data.filter.push(title1)

    let title2 = new FilterTitle()
    title2.name = '年代'
    let labs2 = ["全部","2020年代","2024","2023","2022","2021","2020","2019","2010年代","2000年代","90年代","80年代","70年代","60年代","更早"]
    for (let index = 0; index < labs2.length; index++) {
        const element = labs2[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = element
        if (index === 0) {
            lab.id = ''
        }
        title2.list.push(lab)
    }
    backData.data.filter.push(title2)

    let title3 = new FilterTitle()
    title3.name = '平台'
    let labs3 = ["全部","腾讯视频","爱奇艺","优酷","湖南卫视","Netflix","HBO","BBC","NHK","CBS","NBC","tvN"]
    for (let index = 0; index < labs3.length; index++) {
        const element = labs3[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = element
        if (index === 0) {
            lab.id = ''
        }
        title3.list.push(lab)
    }
    backData.data.filter.push(title3)

    let title4 = new FilterTitle()
    title4.name = '排序'
    let labs4 = ["近期热度","综合排序","首播时间","高分优先"]
    let ids = ['U', '', 'R', 'S']
    for (let index = 0; index < labs4.length; index++) {
        const element = labs4[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = ids[index]
        title4.list.push(lab)
    }
    backData.data.filter.push(title4)
}

async function getMovieFilterLabsData(backData) {
    // 电影筛选
    let title0 = new FilterTitle()
    title0.name = '类型'
    let labs0 = ["全部","喜剧","爱情","动作","科幻","动画","悬疑","犯罪","惊悚","冒险","音乐","历史","奇幻","恐怖","战争","传记","歌舞","武侠","情色","灾难","西部","纪录片","短片"]
    for (let index = 0; index < labs0.length; index++) {
        const element = labs0[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = index === 0 ? '' : element
        lab.key = '类型'
        title0.list.push(lab)
    }
    backData.data.filter.push(title0)

    let title1 = new FilterTitle()
    title1.name = '地区'
    let labs1 = ["全部","华语","欧美","韩国","日本","中国大陆","美国","中国香港","中国台湾","英国","法国","德国","意大利","西班牙","印度","泰国","俄罗斯","加拿大","澳大利亚","爱尔兰","瑞典","巴西","丹麦"]
    for (let index = 0; index < labs1.length; index++) {
        const element = labs1[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = element
        lab.key = '地区'
        if (index === 0) {
            lab.id = ''
        } else if (index === 1) {
            lab.key = ''
        }
        title1.list.push(lab)
    }
    backData.data.filter.push(title1)

    let title2 = new FilterTitle()
    title2.name = '年代'
    let labs2 = ["全部","2020年代","2024","2023","2022","2021","2020","2019","2010年代","2000年代","90年代","80年代","70年代","60年代","更早"]
    for (let index = 0; index < labs2.length; index++) {
        const element = labs2[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = element
        if (index === 0) {
            lab.id = ''
        }
        title2.list.push(lab)
    }
    backData.data.filter.push(title2)

    let title3 = new FilterTitle()
    title3.name = '平台'
    let labs3 = ["全部","腾讯视频","爱奇艺","优酷","湖南卫视","Netflix","HBO","BBC","NHK","CBS","NBC","tvN"]
    for (let index = 0; index < labs3.length; index++) {
        const element = labs3[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = element
        if (index === 0) {
            lab.id = ''
        }
        title3.list.push(lab)
    }
    backData.data.filter.push(title3)

    let title4 = new FilterTitle()
    title4.name = '排序'
    let labs4 = ["近期热度","综合排序","首播时间","高分优先"]
    let ids = ['U', '', 'R', 'S']
    for (let index = 0; index < labs4.length; index++) {
        const element = labs4[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = ids[index]
        title4.list.push(lab)
    }
    backData.data.filter.push(title4)
}

async function getZYFilterLabsData(backData) {
    // 综艺筛选
    let title0 = new FilterTitle()
    title0.name = '类型'
    let labs0 = ["全部","真人秀","脱口秀","音乐","歌舞"]
    for (let index = 0; index < labs0.length; index++) {
        const element = labs0[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = index === 0 ? '' : element
        lab.key = '类型'
        title0.list.push(lab)
    }
    backData.data.filter.push(title0)

    let title1 = new FilterTitle()
    title1.name = '地区'
    let labs1 = ["全部","华语","欧美","国外","韩国","日本","中国大陆","中国香港","美国","英国","泰国","中国台湾","意大利","法国","德国","西班牙","俄罗斯","瑞典","巴西","丹麦","印度","加拿大","爱尔兰","澳大利亚"]
    for (let index = 0; index < labs1.length; index++) {
        const element = labs1[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = element
        lab.key = '地区'
        if (index === 0) {
            lab.id = ''
        } else if (index === 1) {
            lab.key = ''
        }
        title1.list.push(lab)
    }
    backData.data.filter.push(title1)

    let title2 = new FilterTitle()
    title2.name = '年代'
    let labs2 = ["全部","2020年代","2024","2023","2022","2021","2020","2019","2010年代","2000年代","90年代","80年代","70年代","60年代","更早"]
    for (let index = 0; index < labs2.length; index++) {
        const element = labs2[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = element
        if (index === 0) {
            lab.id = ''
        }
        title2.list.push(lab)
    }
    backData.data.filter.push(title2)

    let title3 = new FilterTitle()
    title3.name = '平台'
    let labs3 = ["全部","腾讯视频","爱奇艺","优酷","湖南卫视","Netflix","HBO","BBC","NHK","CBS","NBC","tvN"]
    for (let index = 0; index < labs3.length; index++) {
        const element = labs3[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = element
        if (index === 0) {
            lab.id = ''
        }
        title3.list.push(lab)
    }
    backData.data.filter.push(title3)

    let title4 = new FilterTitle()
    title4.name = '排序'
    let labs4 = ["近期热度","综合排序","首播时间","高分优先"]
    let ids = ['U', '', 'R', 'S']
    for (let index = 0; index < labs4.length; index++) {
        const element = labs4[index]
        let lab = new FilterLabel()
        lab.name = element
        lab.id = ids[index]
        title4.list.push(lab)
    }
    backData.data.filter.push(title4)
}

/**
 * 获取视频列表
 */
async function getVideoList(args) {
    var backData = new RepVideoList()
    try {
        const typeId = args.url
        const page = args.page || 1

        if (typeId === '1') {
            // 找电视剧 - 筛选列表
            await getTVFilterList(args.filter, backData, page - 1)
        } else if (typeId === '2') {
            // 找电影 - 筛选列表
            await getMovieFilterList(args.filter, backData, page - 1)
        } else if (typeId === '3') {
            // 找综艺 - 筛选列表
            await getZYFilterList(args.filter, backData, page - 1)
        } else if (typeId === 'hot_tv') {
            // 热门电视剧
            await getHotTVList(backData, page - 1)
        } else if (typeId === 'hot_movie') {
            // 热门电影
            await getHotMovieList(backData, page - 1)
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

async function getTVFilterList(filterLabs, backData, page) {
    let param1 = '{"形式":"电视剧",'
    let tags = ''

    if (filterLabs && filterLabs.length > 0) {
        for (let index = 0; index < filterLabs.length - 1; index++) {
            const lab = filterLabs[index]
            if (lab.key !== '') {
                param1 += '"' + lab.key + '":"' + lab.id + '",'
            }
            if (lab.id !== '') {
                tags += lab.id + ','
            }
        }
    }

    param1 = param1.endsWith(',') ? param1.substring(0, param1.length - 1) : param1
    param1 += '}'
    tags = tags.endsWith(',') ? tags.substring(0, tags.length - 1) : tags

    let sort = (filterLabs && filterLabs.length > 0) ? filterLabs[filterLabs.length - 1].id : ''
    let start = page * 20

    let url = `${mobileApiDomain}/tv/recommend?refresh=0&start=${start}&count=20&selected_categories=${encodeURIComponent(param1)}&uncollect=false&tags=${encodeURIComponent(tags)}&sort=${sort}`

    let response = await req(url, { headers: mobileHeaders })
    let list = response?.data?.items ?? []

    for (let index = 0; index < list.length; index++) {
        const element = list[index]
        let video = new VideoDetail()
        video.vod_id = element.id || ''
        video.vod_name = element.title || ''
        video.vod_pic = replaceDouBanImageHost(element.pic?.normal ?? element.pic?.large ?? '')
        video.vod_remarks = '豆瓣' + (element.rating?.value ?? '')
        video.type_name = '电视剧'
        backData.data.push(video)
    }
}

async function getMovieFilterList(filterLabs, backData, page) {
    let param1 = '{'
    let tags = ''

    if (filterLabs && filterLabs.length > 0) {
        for (let index = 0; index < filterLabs.length - 1; index++) {
            const lab = filterLabs[index]
            if (lab.key !== '') {
                param1 += '"' + lab.key + '":"' + lab.id + '",'
            }
            if (lab.id !== '') {
                tags += lab.id + ','
            }
        }
    }

    param1 = param1.endsWith(',') ? param1.substring(0, param1.length - 1) : param1
    param1 += '}'
    tags = tags.endsWith(',') ? tags.substring(0, tags.length - 1) : tags

    let sort = (filterLabs && filterLabs.length > 0) ? filterLabs[filterLabs.length - 1].id : ''
    let start = page * 20

    let url = `${mobileApiDomain}/movie/recommend?refresh=0&start=${start}&count=20&selected_categories=${encodeURIComponent(param1)}&uncollect=false&tags=${encodeURIComponent(tags)}&sort=${sort}`

    let response = await req(url, { headers: mobileHeaders })
    let list = response?.data?.items ?? []

    for (let index = 0; index < list.length; index++) {
        const element = list[index]
        let video = new VideoDetail()
        video.vod_id = element.id || ''
        video.vod_name = element.title || ''
        video.vod_pic = replaceDouBanImageHost(element.pic?.normal ?? element.pic?.large ?? '')
        video.vod_remarks = '豆瓣' + (element.rating?.value ?? '')
        video.type_name = '电影'
        backData.data.push(video)
    }
}

async function getZYFilterList(filterLabs, backData, page) {
    let param1 = '{"形式":"综艺",'
    let tags = '综艺,'

    if (filterLabs && filterLabs.length > 0) {
        for (let index = 0; index < filterLabs.length - 1; index++) {
            const lab = filterLabs[index]
            if (lab.key !== '') {
                param1 += '"' + lab.key + '":"' + lab.id + '",'
            }
            if (lab.id !== '') {
                tags += lab.id + ','
            }
        }
    }

    param1 = param1.endsWith(',') ? param1.substring(0, param1.length - 1) : param1
    param1 += '}'
    tags = tags.endsWith(',') ? tags.substring(0, tags.length - 1) : tags

    let sort = (filterLabs && filterLabs.length > 0) ? filterLabs[filterLabs.length - 1].id : ''
    let start = page * 20

    let url = `${mobileApiDomain}/tv/recommend?refresh=0&start=${start}&count=20&selected_categories=${encodeURIComponent(param1)}&uncollect=false&tags=${encodeURIComponent(tags)}&sort=${sort}`

    let response = await req(url, { headers: mobileHeaders })
    let list = response?.data?.items ?? []

    for (let index = 0; index < list.length; index++) {
        const element = list[index]
        let video = new VideoDetail()
        video.vod_id = element.id || ''
        video.vod_name = element.title || ''
        video.vod_pic = replaceDouBanImageHost(element.pic?.normal ?? element.pic?.large ?? '')
        video.vod_remarks = '豆瓣' + (element.rating?.value ?? '')
        video.type_name = '综艺'
        backData.data.push(video)
    }
}

async function getHotTVList(backData, page) {
    let start = page * 20
    let url = `${mobileApiDomain}/subject_collection/tv_real_time_hotest/items?type=tv&start=${start}&count=20&for_mobile=1`

    let response = await req(url, { headers: mobileHeaders })
    let list = response?.data?.subject_collection_items ?? []

    for (let index = 0; index < list.length; index++) {
        const element = list[index]
        let video = new VideoDetail()
        video.vod_id = element.id || ''
        video.vod_name = element.title || ''
        video.vod_pic = replaceDouBanImageHost(element.cover?.url ?? '')
        video.vod_remarks = '豆瓣' + (element.rating?.value ?? '')
        video.type_name = '电视剧'
        backData.data.push(video)
    }
}

async function getHotMovieList(backData, page) {
    let start = page * 20
    let url = `${mobileApiDomain}/subject_collection/movie_real_time_hotest/items?type=movie&start=${start}&count=20&for_mobile=1`

    let response = await req(url, { headers: mobileHeaders })
    let list = response?.data?.subject_collection_items ?? []

    for (let index = 0; index < list.length; index++) {
        const element = list[index]
        let video = new VideoDetail()
        video.vod_id = element.id || ''
        video.vod_name = element.title || ''
        video.vod_pic = replaceDouBanImageHost(element.cover?.url ?? '')
        video.vod_remarks = '豆瓣' + (element.rating?.value ?? '')
        video.type_name = '电影'
        backData.data.push(video)
    }
}

/**
 * 搜索视频
 */
async function searchVideo(args) {
    var backData = new RepVideoList()
    try {
        const keyword = args.searchWord
        const page = args.page || 1

        if (!keyword) {
            backData.error = '搜索关键词不能为空'
            return JSON.stringify(backData)
        }

        // 使用豆瓣搜索API
        let start = (page - 1) * 20
        let url = `https://m.douban.com/rexxar/api/v2/search/movie?q=${encodeURIComponent(keyword)}&start=${start}&count=20&for_mobile=1`

        let response = await req(url, { headers: mobileHeaders })
        let list = response?.data?.items ?? []

        for (let index = 0; index < list.length; index++) {
            const element = list[index]
            let video = new VideoDetail()
            video.vod_id = element.id || ''
            video.vod_name = element.title || ''
            video.vod_pic = replaceDouBanImageHost(element.cover_url ?? element.pic?.normal ?? '')
            video.vod_remarks = '豆瓣' + (element.rating?.value ?? '')
            video.type_name = element.type_name || '影视'
            backData.data.push(video)
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取视频详情
 */
async function getVideoDetail(args) {
    var backData = new RepVideoDetail()
    try {
        const vodId = args.url

        if (!vodId) {
            backData.error = '视频ID不能为空'
            return JSON.stringify(backData)
        }

        // 使用豆瓣详情API
        let url = `https://m.douban.com/rexxar/api/v2/movie/${vodId}?for_mobile=1`

        let response = await req(url, { headers: mobileHeaders })
        let data = response?.data

        if (data) {
            let video = new VideoDetail()
            video.vod_id = data.id || ''
            video.vod_name = data.title || ''
            video.vod_pic = replaceDouBanImageHost(data.pic?.normal ?? data.cover_url ?? '')
            video.vod_remarks = '豆瓣' + (data.rating?.value ?? '')
            video.vod_year = data.year || ''
            video.vod_area = data.countries?.join(',') || ''
            video.vod_lang = data.languages?.join(',') || ''
            video.vod_director = data.directors?.map(d => d.name).join(',') || ''
            video.vod_actor = data.actors?.map(a => a.name).join(',') || ''
            video.vod_content = data.intro || ''
            video.type_name = data.type || '影视'
            video.vod_douban_score = data.rating?.value || ''

            // 播放信息（豆瓣本身不提供播放源，这里只是示例）
            video.vod_play_from = '豆瓣'
            video.vod_play_url = '暂无播放源$' + data.url

            backData.data = video
        } else {
            backData.error = '获取视频详情失败'
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取播放地址
 */
async function getVideoPlayUrl(args) {
    var backData = new RepVideoPlayUrl()
    try {
        // 豆瓣本身不提供播放源，这里返回空数据
        backData.error = '豆瓣不提供播放源，仅供查看影片信息'
    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}
