//@name:[盘] 斑斑
//@version:1
//@webSite:http://xsayang.fun:12512/
//@remark:
//@order: B


/// 是否模拟 PC 是 1， 手机是 0
const isUsePC = 1
/// 默认应该是 0，当视频不能播放的时候，可以把这个设置为 1， 否则不要改动
const isAddReferer = 0

/// 当前网站是不是网盘资源分享站 0 不是，1 是
const isPan = 1

/// 匹配的网盘类型，一般不需要改动
const panUrls = [
    '189.cn', //天翼
    '123684.com', // 123
    '123865.com',
    '123912.com',
    '123pan.com',
    '123pan.cn',
    'pan.quark.cn', // 夸克
    'drive.uc.cn', // uc
]

// 网站主页
const webSite = 'http://xsayang.fun:12512/'

// 网站搜索
// http://xsayang.fun:12512/index.php/vod/search/page/1/wd/仙逆.html
// 把网站主页变成 @{webSite} 把搜索词变成 @{searchWord}  把页码变成 @{page}
const searchUrl = '@{webSite}/index.php/vod/search/page/@{page}/wd/@{searchWord}.html'

// 当前网站任意视频详情页
// http://xsayang.fun:12512/index.php/vod/play/id/427416/sid/1/nid/1.html
const videoDetailPage = '@{webSite}/index.php/vod/detail/id/@{id}.html'

// 当 isPan = 1 时，表明是资源分享站 这个不用改动。
// 当前网站任意视频播放页
const videoPlayPage = ''

// 保持不变
const filterListUrl = ''

const firstClass = [
    {
        name: '影',
        // http://xsayang.fun:12512/index.php/vod/show/id/1/page/1.html
        // 把网站主页变成 @{webSite}  把页码变成 @{page}
        id: '@{webSite}/index.php/vod/show/id/1/page/@{page}.html',
    },
    {
        name: '剧',
        // http://xsayang.fun:12512/index.php/vod/show/id/2/page/1.html
        // 把网站主页变成 @{webSite}  把页码变成 @{page}
        id: '@{webSite}/index.php/vod/show/id/2/page/@{page}.html',
    },
    {
        name: '综',
        // http://xsayang.fun:12512/index.php/vod/show/id/3/page/1.html
        // 把网站主页变成 @{webSite}  把页码变成 @{page}
        id: '@{webSite}/index.php/vod/show/id/3/page/@{page}.html',
    },
    {
        name: '漫',
        // http://xsayang.fun:12512/index.php/vod/show/id/4/page/1.html
        // 把网站主页变成 @{webSite}  把页码变成 @{page}
        id: '@{webSite}/index.php/vod/show/id/4/page/@{page}.html',
    },
    {
        name: '精',
        // http://xsayang.fun:12512/index.php/vod/show/id/5/page/1.html
        // 把网站主页变成 @{webSite}  把页码变成 @{page}
        id: '@{webSite}/index.php/vod/show/id/5/page/@{page}.html',
    },
    {
        name: '短',
        // http://xsayang.fun:12512/index.php/vod/show/id/6/page/1.html
        // 把网站主页变成 @{webSite}  把页码变成 @{page}
        id: '@{webSite}/index.php/vod/show/id/6/page/@{page}.html',
    },
    {
        name: '翼',
        // http://xsayang.fun:12512/index.php/vod/show/id/36/page/1.html
        // 把网站主页变成 @{webSite}  把页码变成 @{page}
        id: '@{webSite}/index.php/vod/show/id/36/page/@{page}.html',
    },
    {
        name: '123',
        // http://xsayang.fun:12512/index.php/vod/show/id/35/page/1.html
        // 把网站主页变成 @{webSite}  把页码变成 @{page}
        id: '@{webSite}/index.php/vod/show/id/35/page/@{page}.html',
    }
]

// 下面这个不要有任何改动，且保持在最后一行，加载内置代码需要
// 下面这个不要有任何改动，且保持在最后一行，加载内置代码需要
// 下面这个不要有任何改动，且保持在最后一行，加载内置代码需要

//#BaseCode1#