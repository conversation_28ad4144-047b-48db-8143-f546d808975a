// ignore

//@name:🟢 豆瓣增强版
//@webSite:https://frodo.douban.com
//@version:1
//@remark:🍃豆儿出品，增强版！集成签名算法
//@codeID:
//@order: A

// ignore

// UZ平台豆瓣增强版
// 在精简版基础上集成标准版的签名算法和API

// 由于UZ不支持导入，直接定义所需的类
class VideoDetail {
    constructor() {
        this.vod_id = ''
        this.vod_name = ''
        this.vod_play_from = ''
        this.vod_play_url = ''
        this.vod_pic = ''
        this.type_name = ''
        this.vod_remarks = ''
        this.vod_douban_score = ''
        this.vod_lang = ''
        this.vod_year = ''
        this.vod_actor = ''
        this.vod_director = ''
        this.vod_content = ''
        this.vod_area = ''
        this.panUrls = []
    }
}

class VideoClass {
    constructor() {
        this.type_id = ''
        this.type_name = ''
        this.hasSubclass = false
    }
}

class FilterLabel {
    constructor() {
        this.name = ''
        this.id = ''
        this.key = ''
    }
}

class FilterTitle {
    constructor() {
        this.name = ''
        this.list = []
    }
}

class RepVideoClassList {
    constructor() {
        this.error = ''
        this.data = []
    }
}

class RepVideoSubclassList {
    constructor() {
        this.error = ''
        this.data = {
            filter: []
        }
    }
}

class RepVideoList {
    constructor() {
        this.error = ''
        this.data = []
        this.total = 0
    }
}

class RepVideoDetail {
    constructor() {
        this.error = ''
        this.data = null
    }
}

class RepVideoPlayUrl {
    constructor() {
        this.error = ''
        this.data = []
    }
}

const appConfig = {
    _webSite: 'https://frodo.douban.com',
    get webSite() {
        return this._webSite
    },
    set webSite(value) {
        this._webSite = value
    },

    _uzTag: '',
    get uzTag() {
        return this._uzTag
    },
    set uzTag(value) {
        this._uzTag = value
    },
}

// 豆瓣内部API域名（增强版使用内部API获取更丰富数据）
const apiDomain = 'https://frodo.douban.com'

// 设备信息管理
let device = {}

// 生成随机字符串
function generateRandomId(length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
}

// 获取Unix时间戳
function getUnixTimestamp() {
    return Math.floor(Date.now() / 1000)
}

// 初始化设备信息
function initDevice() {
    const deviceKey = 'douban_device_enhanced'
    device = UZUtils.getStorage(deviceKey) || {}
    
    if (!device.id) {
        device.id = generateRandomId(40).toLowerCase()
        device.ua = `Rexxar-Core/0.1.3 api-client/1 com.douban.frodo/7.9.0(216) Android/28 product/Xiaomi11 rom/android network/wifi udid/${device.id} platform/mobile com.douban.frodo/7.9.0(216) Rexxar/1.2.151 platform/mobile 1.2.151`
        UZUtils.setStorage(deviceKey, device)
    }
    
    return device
}

// 简单的URL解析函数
function parseUrl(url) {
    const match = url.match(/^(https?:\/\/[^\/]+)(\/[^?]*)?(\?.*)?$/)
    if (match) {
        return {
            origin: match[1],
            pathname: match[2] || '/',
            search: match[3] || ''
        }
    }
    return { origin: '', pathname: '/', search: '' }
}

// 豆瓣API签名函数
function sig(link) {
    try {
        const CryptoJS = createCryptoJS()

        link += `&udid=${device.id}&uuid=${device.id}&&rom=android&apikey=0dad551ec0f84ed02907ff5c42e8ec70&s=rexxar_new&channel=Yingyongbao_Market&timezone=Asia/Shanghai&device_id=${device.id}&os_rom=android&apple=c52fbb99b908be4d026954cc4374f16d&mooncake=0f607264fc6318a92b9e13c65db7cd3c&sugar=0`

        const u = parseUrl(link)
        const ts = getUnixTimestamp().toString()
        let sha1 = CryptoJS.HmacSHA1('GET&' + encodeURIComponent(u.pathname) + '&' + ts, 'bf7dddc7c9cfe6f7')
        let signa = CryptoJS.enc.Base64.stringify(sha1)

        return link + '&_sig=' + encodeURIComponent(signa) + '&_ts=' + ts
    } catch (error) {
        // 如果签名失败，返回原链接（降级处理）
        return link + `&udid=${device.id}&uuid=${device.id}&&rom=android&apikey=0dad551ec0f84ed02907ff5c42e8ec70&s=rexxar_new&channel=Yingyongbao_Market&timezone=Asia/Shanghai&device_id=${device.id}&os_rom=android&apple=c52fbb99b908be4d026954cc4374f16d&mooncake=0f607264fc6318a92b9e13c65db7cd3c&sugar=0&_ts=${getUnixTimestamp()}`
    }
}

// 网络请求封装
async function request(reqUrl, ua) {
    try {
        const response = await req(reqUrl, {
            headers: {
                'User-Agent': ua || device.ua,
            },
        })
        
        if (response.code === 200) {
            return response.data
        } else {
            throw new Error(`HTTP ${response.code}`)
        }
    } catch (error) {
        throw new Error(`请求失败: ${error.message}`)
    }
}

// 用于控制欢迎提示只显示一次
let hasShownWelcome = false

/**
 * 获取分类列表
 */
async function getClassList() {
    var backData = new RepVideoClassList()
    try {
        // 初始化设备信息
        initDevice()
        
        // 首次加载时显示欢迎提示
        if (!hasShownWelcome) {
            hasShownWelcome = true
            toast("🍃豆儿出品，增强版！", 3)
        }

        const classes = [
            {
                type_id: 't1',
                type_name: '热播',
                hasSubclass: true
            },
            {
                type_id: 't2',
                type_name: '片库',
                hasSubclass: true
            },
            {
                type_id: 't250',
                type_name: 'Top250',
                hasSubclass: false
            },
            {
                type_id: 't3',
                type_name: '榜单',
                hasSubclass: false
            },
            {
                type_id: 't4',
                type_name: '片单',
                hasSubclass: true
            },
        ]

        backData.data = classes.map(item => {
            const videoClass = new VideoClass()
            videoClass.type_id = item.type_id
            videoClass.type_name = item.type_name
            videoClass.hasSubclass = item.hasSubclass
            return videoClass
        })

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取二级分类列表筛选列表
 */
async function getSubclassList(args) {
    var backData = new RepVideoSubclassList()
    try {
        const typeId = args.url

        if (typeId === 't1') {
            // 热播分类的筛选
            const filterTitle = new FilterTitle()
            filterTitle.name = '类型'

            const options = [
                { name: '电影', id: 'movie/hot_gaia' },
                { name: '电视剧', id: 'subject_collection/tv_hot/items' },
                { name: '国产剧', id: 'subject_collection/tv_domestic/items' },
                { name: '美剧', id: 'subject_collection/tv_american/items' },
                { name: '日剧', id: 'subject_collection/tv_japanese/items' },
                { name: '韩剧', id: 'subject_collection/tv_korean/items' },
                { name: '动漫', id: 'subject_collection/tv_animation/items' },
                { name: '综艺', id: 'subject_collection/show_hot/items' },
            ]

            filterTitle.list = options.map(opt => {
                const label = new FilterLabel()
                label.name = opt.name
                label.id = opt.id
                label.key = 'u'
                return label
            })

            backData.data.filter = [filterTitle]
        } else if (typeId === 't2') {
            // 片库的筛选 - 需要获取豆瓣的标签数据
            const link = sig(`${apiDomain}/api/v2/movie/tag?sort=U&start=0&count=30&q=全部形式,全部类型,全部地区,全部年代&score_rang=0,10`)
            const data = await request(link)

            let filterAll = []
            for (const tag of data.tags) {
                if (tag.type == '特色') continue

                const filterTitle = new FilterTitle()
                filterTitle.name = tag.type

                let fValues = []
                if (tag.type == '年代' && tag.data.indexOf(new Date().getFullYear().toString()) < 0) {
                    tag.data.splice(1, 0, new Date().getFullYear().toString())
                    if (tag.data.indexOf((new Date().getFullYear() - 1).toString()) < 0) {
                        tag.data.splice(2, 0, (new Date().getFullYear() - 1).toString())
                    }
                }

                for (const v of tag.data) {
                    const label = new FilterLabel()
                    label.name = v.indexOf('全部') >= 0 ? '全部' : v
                    label.id = v
                    label.key = tag.type
                    fValues.push(label)
                }

                filterTitle.list = fValues
                filterAll.push(filterTitle)
            }

            // 添加排序选项
            const sortFilter = new FilterTitle()
            sortFilter.name = '排序'
            sortFilter.list = data.sorts.map(sort => {
                const label = new FilterLabel()
                label.name = sort.text
                label.id = sort.name
                label.key = 'sort'
                return label
            })
            filterAll.push(sortFilter)

            backData.data.filter = filterAll
        } else if (typeId === 't4') {
            // 片单的筛选
            const filters = [
                {
                    name: '类型',
                    key: 'type',
                    options: [
                        { name: '全部', id: '' },
                        { name: '电影', id: 'movie' },
                        { name: '电视剧', id: 'tv' },
                    ]
                },
                {
                    name: '分类',
                    key: 'cate',
                    options: [
                        { name: '全部', id: 'all' },
                        { name: '豆瓣片单', id: 'official' },
                        { name: '精选', id: 'selected' },
                        { name: '经典', id: 'classical' },
                        { name: '获奖', id: 'prize' },
                        { name: '高分', id: 'high_score' },
                        { name: '榜单', id: 'movie_list' },
                        { name: '冷门佳片', id: 'dark_horse' },
                        { name: '主题', id: 'topic' },
                        { name: '导演', id: 'director' },
                        { name: '演员', id: 'actor' },
                        { name: '系列', id: 'series' },
                    ]
                },
                {
                    name: '地区',
                    key: 'region',
                    options: [
                        { name: '华语', id: 'chinese' },
                        { name: '欧美', id: 'western' },
                        { name: '日本', id: 'japanese' },
                        { name: '韩国', id: 'korea' },
                    ]
                }
            ]

            backData.data.filter = filters.map(filter => {
                const filterTitle = new FilterTitle()
                filterTitle.name = filter.name
                filterTitle.list = filter.options.map(opt => {
                    const label = new FilterLabel()
                    label.name = opt.name
                    label.id = opt.id
                    label.key = filter.key
                    return label
                })
                return filterTitle
            })
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取分类视频列表
 */
async function getVideoList(args) {
    var backData = new RepVideoList()
    try {
        const typeId = args.url
        const page = args.page || 1
        const filters = args.filter || {}

        if (typeId === 't1') {
            // 热播分类
            const apiPath = filters.u || 'movie/hot_gaia'
            const doubanUrl = sig(`${apiDomain}/api/v2/${apiPath}?area=全部&sort=recommend&playable=0&loc_id=0&start=${(page - 1) * 30}&count=30`)
            const data = await request(doubanUrl)

            const items = data.items || data.subject_collection_items || []
            const videos = []

            for (const item of items) {
                const video = new VideoDetail()
                video.vod_id = item.id
                video.vod_name = item.title
                video.vod_pic = (item.pic && item.pic.normal) ? item.pic.normal.replace(/^http:/, 'https:') : ''

                const score = (item.rating ? item.rating.value || '' : '').toString()
                video.vod_remarks = score.length > 0 ? '评分:' + score : ''
                video.vod_douban_score = score

                videos.push(video)
            }

            backData.data = videos
            backData.total = Math.ceil((data.total || videos.length) / 30)
        } else if (typeId === 't250') {
            // Top250
            const doubanUrl = sig(`${apiDomain}/api/v2/subject_collection/movie_top250/items?start=${(page - 1) * 30}&count=30`)
            const data = await request(doubanUrl)

            const items = data.subject_collection_items || []
            const videos = []

            for (const item of items) {
                const video = new VideoDetail()
                video.vod_id = item.id
                video.vod_name = item.title
                video.vod_pic = (item.pic && item.pic.normal) ? item.pic.normal.replace(/^http:/, 'https:') : ''

                const score = (item.rating ? item.rating.value || '' : '').toString()
                video.vod_remarks = score.length > 0 ? '评分:' + score : ''
                video.vod_douban_score = score

                videos.push(video)
            }

            backData.data = videos
            backData.total = Math.ceil(data.total / 30)
        } else if (typeId === 't2') {
            // 片库筛选
            const sort = filters.sort || 'U'
            const form = filters['形式'] || '全部形式'
            const type = filters['类型'] || '全部类型'
            const area = filters['地区'] || '全部地区'
            const year = filters['年代'] || '全部年代'

            const link = sig(`${apiDomain}/api/v2/movie/tag?sort=${sort}&start=${(page - 1) * 30}&count=30&q=${form},${type},${area},${year}&score_rang=0,10`)
            const data = await request(link)

            const videos = []
            for (const vod of data.data) {
                const video = new VideoDetail()
                video.vod_id = vod.id
                video.vod_name = vod.title
                video.vod_pic = (vod.pic && vod.pic.normal) ? vod.pic.normal.replace(/^http:/, 'https:') : ''

                const score = (vod.rating ? vod.rating.value || '' : '').toString()
                video.vod_remarks = score.length > 0 ? '评分:' + score : ''
                video.vod_douban_score = score

                videos.push(video)
            }

            backData.data = videos
            backData.total = Math.ceil(data.total / 30)
        } else if (typeId === 't3') {
            // 榜单
            const doubanUrl = sig(`${apiDomain}/api/v2/subject_collection/movie_billboard/items?start=${(page - 1) * 30}&count=30`)
            const data = await request(doubanUrl)

            const items = data.subject_collection_items || []
            const videos = []

            for (const item of items) {
                const video = new VideoDetail()
                video.vod_id = item.id
                video.vod_name = item.title
                video.vod_pic = (item.pic && item.pic.normal) ? item.pic.normal.replace(/^http:/, 'https:') : ''

                const score = (item.rating ? item.rating.value || '' : '').toString()
                video.vod_remarks = score.length > 0 ? '评分:' + score : ''
                video.vod_douban_score = score

                videos.push(video)
            }

            backData.data = videos
            backData.total = Math.ceil(data.total / 30)
        } else if (typeId === 't4') {
            // 片单
            const type = filters.type || ''
            const cate = filters.cate || 'all'
            const region = filters.region || ''

            let apiPath = 'subject_collection/filter'
            let queryParams = `start=${(page - 1) * 30}&count=30`

            if (type) queryParams += `&type=${type}`
            if (cate && cate !== 'all') queryParams += `&cate=${cate}`
            if (region) queryParams += `&region=${region}`

            const doubanUrl = sig(`${apiDomain}/api/v2/${apiPath}?${queryParams}`)
            const data = await request(doubanUrl)

            const items = data.subject_collections || []
            const videos = []

            for (const item of items) {
                const video = new VideoDetail()
                video.vod_id = 'collection_' + item.id
                video.vod_name = item.title
                video.vod_pic = (item.cover_url || '').replace(/^http:/, 'https:')
                video.vod_remarks = `${item.subject_count || 0}部作品`
                video.vod_content = item.description || ''

                videos.push(video)
            }

            backData.data = videos
            backData.total = Math.ceil(data.total / 30)
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取二级分类视频列表
 */
async function getSubclassVideoList(args) {
    // 直接调用 getVideoList，避免代码重复
    return await getVideoList({ url: args.mainClassId, page: args.page })
}

/**
 * 获取视频详情
 */
async function getVideoDetail(args) {
    var backData = new RepVideoDetail()
    try {
        const videoDetail = new VideoDetail()
        videoDetail.vod_id = args.url
        videoDetail.vod_name = '豆瓣影视详情'
        videoDetail.vod_content = '这是来自豆瓣的影视信息，仅供参考，不提供播放功能。'
        videoDetail.vod_remarks = '仅展示信息'
        backData.data = videoDetail
    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取视频播放地址
 */
async function getVideoPlayUrl() {
    var backData = new RepVideoPlayUrl()
    backData.error = '豆瓣仅提供影视信息展示，不提供播放功能'
    return JSON.stringify(backData)
}

/**
 * 搜索视频
 */
async function searchVideo(args) {
    var backData = new RepVideoList()
    try {
        const keyword = args.searchWord
        const page = args.page || 1

        if (!keyword) {
            backData.error = '请输入搜索关键词'
            return JSON.stringify(backData)
        }

        // 使用豆瓣搜索API
        const searchUrl = sig(`${apiDomain}/api/v2/search/movie?q=${encodeURIComponent(keyword)}&start=${(page - 1) * 25}&count=25`)
        const data = await request(searchUrl)

        if (data.items && Array.isArray(data.items)) {
            const videoList = []
            for (let i = 0; i < data.items.length; i++) {
                try {
                    const item = data.items[i]
                    const videoDetail = new VideoDetail()
                    videoDetail.vod_id = 'douban_search_' + (item.title || 'unknown') + '_' + (item.id || i)
                    videoDetail.vod_name = item.title || ''

                    if (item.pic && typeof item.pic === 'object') {
                        videoDetail.vod_pic = (item.pic.normal || item.pic.large || '').replace(/^http:/, 'https:')
                    }

                    const score = item.rating ? (item.rating.value || '') : ''
                    videoDetail.vod_douban_score = score.toString()
                    videoDetail.vod_remarks = score ? '⭐ ' + score : '⭐ 暂无评分'
                    videoDetail.type_name = item.subtype === 'movie' ? '电影' : '电视剧'
                    videoDetail.vod_content = '豆瓣搜索结果 评分：' + (score || '暂无评分')

                    videoList.push(videoDetail)
                } catch (itemError) {
                    // 忽略单个条目错误，继续处理其他条目
                }
            }

            backData.data = videoList
            backData.total = videoList.length
        } else {
            backData.error = '搜索结果为空或格式异常'
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}
