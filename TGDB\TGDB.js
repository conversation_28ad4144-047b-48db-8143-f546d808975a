// ignore

//@name:✨豆瓣
//@webSite:https://movie.douban.com
//@version:2
//@remark:🍃豆儿出品，不属精品！集成网盘搜索功能
//@env:网盘搜API地址##https://pansou.252035.xyz/api/search
//@codeID:
//@order: A

// ignore

// 由于uz不支持导入，直接定义所需的类

class VideoDetail {
    constructor() {
        this.vod_id = ''
        this.vod_name = ''
        this.vod_play_from = ''
        this.vod_play_url = ''
        this.vod_pic = ''
        this.type_name = ''
        this.vod_remarks = ''
        this.vod_douban_score = ''
        this.vod_lang = ''
        this.vod_year = ''
        this.vod_actor = ''
        this.vod_director = ''
        this.vod_content = ''
        this.vod_area = ''
        this.panUrls = []
    }
}

class VideoClass {
    constructor() {
        this.type_id = ''
        this.type_name = ''
        this.hasSubclass = false
    }
}

class FilterLabel {
    constructor() {
        this.name = ''
        this.id = ''
        this.key = ''
    }
}

class FilterTitle {
    constructor() {
        this.name = ''
        this.list = []
    }
}

class RepVideoClassList {
    constructor() {
        this.error = ''
        this.data = []
    }
}

class RepVideoSubclassList {
    constructor() {
        this.error = ''
        this.data = {
            filter: []
        }
    }
}

class RepVideoList {
    constructor() {
        this.error = ''
        this.data = []
        this.total = 0
    }
}

class RepVideoDetail {
    constructor() {
        this.error = ''
        this.data = null
    }
}

class RepVideoPlayUrl {
    constructor() {
        this.error = ''
        this.data = []
    }
}

const appConfig = {
    _webSite: 'https://movie.douban.com',
    get webSite() {
        return this._webSite
    },
    set webSite(value) {
        this._webSite = value
    },

    _uzTag: '',
    get uzTag() {
        return this._uzTag
    },
    set uzTag(value) {
        this._uzTag = value
    },
}

// 网盘搜索配置
const panConfig = {
    apiUrl: 'https://pansou.252035.xyz/api/search'
}

// 网盘配置
const CLOUD_PROVIDERS = {
    tianyi: {
        name: '天翼',
        domains: ['189.cn']
    },
    quark: {
        name: '夸克',
        domains: ['pan.quark.cn']
    },
    uc: {
        name: 'UC',
        domains: ['drive.uc.cn']
    },
    ali: {
        name: '阿里',
        domains: ['alipan.com', 'aliyundrive.com']
    },
    pan123: {
        name: '123',
        domains: ['123pan.com', '123pan.cn', '123684.com', '123912.com', '123865.com']
    }
}

// 从统一配置自动生成所需数组
const panUrlsExt = Object.values(CLOUD_PROVIDERS).flatMap(provider => provider.domains)

// 预编译网盘提供商正则表达式，提高匹配性能
const providerRegexMap = Object.values(CLOUD_PROVIDERS).map(provider => ({
    name: provider.name,
    regex: new RegExp(provider.domains.map(domain =>
        domain.replace(/\./g, '\\.')
    ).join('|'), 'i')
}))

// 用于控制欢迎提示只显示一次
let hasShownWelcome = false

/**
 * 获取分类列表
 */
async function getClassList() {
    var backData = new RepVideoClassList()
    try {
        // 首次加载时显示欢迎提示
        if (!hasShownWelcome) {
            hasShownWelcome = true
            toast("🍃豆儿出品，不属精品！", 3)
        }

        const classes = [
            { type_id: 'tv_hot', type_name: '热门剧集', hasSubclass: false },
            { type_id: 'movie_hot', type_name: '热门电影', hasSubclass: false },
            { type_id: 'movie_top250', type_name: 'Top250', hasSubclass: false },
            { type_id: 'tv_domestic', type_name: '国产剧', hasSubclass: false },
            { type_id: 'tv_variety', type_name: '综艺', hasSubclass: false },
            { type_id: 'tv_documentary', type_name: '纪录', hasSubclass: false },
            { type_id: 'tv_anime', type_name: '日漫', hasSubclass: false },
            { type_id: 'tv_hongkong', type_name: '港剧', hasSubclass: false },
            { type_id: 'tv_japanese', type_name: '日剧', hasSubclass: false },
            { type_id: 'tv_korean', type_name: '韩剧', hasSubclass: false },
            { type_id: 'tv_american', type_name: '美剧', hasSubclass: false },
            { type_id: 'tv_british', type_name: '英剧', hasSubclass: false }
        ]

        backData.data = classes.map(item => {
            const videoClass = new VideoClass()
            videoClass.type_id = item.type_id
            videoClass.type_name = item.type_name
            videoClass.hasSubclass = item.hasSubclass
            return videoClass
        })

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取二级分类列表筛选列表
 */
async function getSubclassList() {
    var backData = new RepVideoSubclassList()
    return JSON.stringify(backData)
}

/**
 * 获取分类视频列表
 */
async function getVideoList(args) {
    var backData = new RepVideoList()
    try {
        const typeId = args.url
        const page = args.page || 1
        const pageStart = (page - 1) * 25

        let type = 'movie'
        let tag = '热门'

        // 根据分类ID设置类型和标签
        if (typeId === 'tv_hot') {
            type = 'tv'
            tag = '热门'
        } else if (typeId === 'movie_top250') {
            return await getTop250Movies(page, backData)
        } else if (typeId === 'tv_domestic') {
            type = 'tv'
            tag = '国产剧'
        } else if (typeId === 'tv_variety') {
            type = 'tv'
            tag = '综艺'
        } else if (typeId === 'tv_documentary') {
            type = 'tv'
            tag = '纪录片'
        } else if (typeId === 'tv_anime') {
            type = 'tv'
            tag = '日本动画'
        } else if (typeId === 'tv_hongkong') {
            type = 'tv'
            tag = '港剧'
        } else if (typeId === 'tv_japanese') {
            type = 'tv'
            tag = '日剧'
        } else if (typeId === 'tv_korean') {
            type = 'tv'
            tag = '韩剧'
        } else if (typeId === 'tv_american') {
            type = 'tv'
            tag = '美剧'
        } else if (typeId === 'tv_british') {
            type = 'tv'
            tag = '英剧'
        }

        const doubanUrl = `https://movie.douban.com/j/search_subjects?type=${type}&tag=${tag}&sort=recommend&page_limit=25&page_start=${pageStart}`

        const response = await req(doubanUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://movie.douban.com/',
                'Accept': 'application/json, text/plain, */*',
            }
        })

        if (response.code === 200) {
            let data
            if (typeof response.data === 'object') {
                data = response.data
            } else if (typeof response.data === 'string') {
                try {
                    data = JSON.parse(response.data)
                } catch (parseError) {
                    backData.error = 'JSON解析失败'
                    return JSON.stringify(backData)
                }
            } else {
                backData.error = '未知的响应数据类型'
                return JSON.stringify(backData)
            }

            if (data.subjects && Array.isArray(data.subjects)) {
                const videoList = []
                for (let i = 0; i < data.subjects.length; i++) {
                    try {
                        const item = data.subjects[i]
                        const videoDetail = new VideoDetail()
                        videoDetail.vod_id = 'douban_' + (item.title || 'unknown') + '_' + (item.id || i)
                        videoDetail.vod_name = item.title || ''

                        if (item.cover && typeof item.cover === 'string') {
                            videoDetail.vod_pic = item.cover.replace(/^http:/, 'https:')
                        }

                        videoDetail.vod_douban_score = item.rate || ''
                        videoDetail.vod_remarks = item.rate ? '⭐ ' + item.rate : '⭐ 暂无评分'
                        videoDetail.type_name = type === 'movie' ? '电影' : '电视剧'
                        videoDetail.vod_content = '豆瓣评分：' + (item.rate || '暂无评分')

                        videoList.push(videoDetail)
                    } catch (itemError) {
                        // 忽略单个条目错误，继续处理其他条目
                    }
                }

                backData.data = videoList
                backData.total = videoList.length
            } else {
                backData.error = '豆瓣API返回数据格式异常'
            }
        } else {
            backData.error = '获取豆瓣数据失败'
        }

    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取二级分类视频列表
 */
async function getSubclassVideoList(args) {
    // 直接调用 getVideoList，避免代码重复
    return await getVideoList({ url: args.mainClassId, page: args.page })
}

/**
 * 获取视频详情 - 集成网盘搜索
 */
async function getVideoDetail(args) {
    var backData = new RepVideoDetail()
    try {
        const videoDetail = new VideoDetail()
        videoDetail.vod_id = args.url

        // 从 vod_id 提取标题用于搜索
        const searchTitle = extractTitleFromVodId(args.url)

        if (searchTitle) {
            videoDetail.vod_name = searchTitle
            videoDetail.vod_content = `豆瓣影视信息：${searchTitle}`
            videoDetail.vod_remarks = '豆瓣信息'

            // 尝试搜索网盘资源
            try {
                const panUrls = await searchPanResources(searchTitle)
                if (panUrls && panUrls.length > 0) {
                    videoDetail.panUrls = panUrls
                    const providers = identifyProviders(panUrls)
                    const providerText = providers.length > 0 ? ` (${providers.join('/')})` : ''
                    videoDetail.vod_remarks = `豆瓣信息 | 🔗 ${panUrls.length}个网盘资源${providerText}`
                }
            } catch (panError) {
                // 网盘搜索失败不影响基础信息展示
            }
        } else {
            // 降级处理：只显示基础信息
            videoDetail.vod_name = '豆瓣影视详情'
            videoDetail.vod_content = '这是来自豆瓣的影视信息，仅供参考。'
            videoDetail.vod_remarks = '仅展示信息'
        }

        backData.data = videoDetail

    } catch (error) {
        // 全局错误处理
        const fallbackDetail = new VideoDetail()
        fallbackDetail.vod_id = args.url
        fallbackDetail.vod_name = '影视详情'
        fallbackDetail.vod_content = '详情获取失败，请稍后重试'
        fallbackDetail.vod_remarks = '获取失败'
        backData.data = fallbackDetail

        console.error('getVideoDetail error:', error)
    }

    return JSON.stringify(backData)
}

/**
 * 获取Top250电影列表
 */
async function getTop250Movies(page, backData) {
    try {
        const start = (page - 1) * 25
        const url = `https://movie.douban.com/top250?start=${start}&filter=`

        const response = await req(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://movie.douban.com/'
            }
        })

        if (response.code === 200) {
            const html = response.data
            const movies = parseTop250Html(html)
            backData.data = movies
            backData.total = movies.length
        } else {
            backData.error = 'Top250页面获取失败'
        }

    } catch (error) {
        backData.error = error.toString()
    }

    return JSON.stringify(backData)
}

/**
 * 解析Top250 HTML页面
 */
function parseTop250Html(html) {
    const movies = []

    try {
        const itemRegex = /<div class="item">[\s\S]*?<\/div>\s*<\/div>\s*<\/div>/g
        const items = html.match(itemRegex) || []

        for (let i = 0; i < items.length; i++) {
            const item = items[i]

            const titleMatch = item.match(/<span class="title">([^<]+)<\/span>/)
            const title = titleMatch ? titleMatch[1] : ''

            const ratingMatch = item.match(/<span class="rating_num"[^>]*>([^<]+)<\/span>/)
            const rating = ratingMatch ? ratingMatch[1] : ''

            const picMatch = item.match(/<img[^>]+src="([^"]+)"/)
            const pic = picMatch ? picMatch[1] : ''

            const idMatch = item.match(/subject\/(\d+)\//)
            const doubanId = idMatch ? idMatch[1] : ''

            if (title) {
                const videoDetail = new VideoDetail()
                videoDetail.vod_id = 'douban_top250_' + doubanId + '_' + i
                videoDetail.vod_name = title
                videoDetail.vod_pic = pic.replace(/^http:/, 'https:')
                videoDetail.vod_douban_score = rating
                videoDetail.vod_remarks = rating ? '⭐ ' + rating : '⭐ 暂无评分'
                videoDetail.type_name = '电影'
                videoDetail.vod_content = '豆瓣Top250 评分：' + rating

                movies.push(videoDetail)
            }
        }

    } catch (error) {
        // 解析失败时返回空数组
    }

    return movies
}

/**
 * 获取视频播放地址
 */
async function getVideoPlayUrl() {
    var backData = new RepVideoPlayUrl()
    backData.error = '豆瓣仅提供影视信息展示，不提供播放功能'
    return JSON.stringify(backData)
}

/**
 * 搜索视频
 */
async function searchVideo() {
    var backData = new RepVideoList()
    backData.error = '搜索功能开发中'
    return JSON.stringify(backData)
}

// ===== 网盘搜索相关函数 =====

/**
 * 从豆瓣 vod_id 提取标题
 */
function extractTitleFromVodId(vodId) {
    if (!vodId) return ''

    // douban2.js 的 vod_id 格式：'douban_' + title + '_' + id
    // 由于标题可能包含特殊字符，我们从最后一个下划线分割
    if (vodId.startsWith('douban_')) {
        const parts = vodId.split('_')
        if (parts.length >= 3) {
            // 移除第一个 'douban' 和最后一个 id，中间的就是标题
            const titleParts = parts.slice(1, -1)
            return titleParts.join('_').trim()
        }
    }

    return ''
}

/**
 * 核心网盘搜索函数
 */
async function searchPanResources(keyword) {
    try {
        // 获取环境变量中的API地址
        let panApi = panConfig.apiUrl
        try {
            const envApi = await getEnv(appConfig.uzTag, "网盘搜API地址")
            if (envApi && envApi.length > 0) {
                panApi = envApi
            }
        } catch (envError) {
            // 忽略环境变量获取失败
        }

        // 构建搜索URL，添加默认频道
        const searchUrl = `${panApi}?kw=${encodeURIComponent(keyword)}&res=merge&channels=zyfb123,tianyirigeng`

        // 发起搜索请求（10秒超时）
        const result = await Promise.race([
            req(searchUrl),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('搜索超时')), 10000)
            )
        ])

        if (result && result.code === 0 && result.data && result.data.merged_by_type) {
            // 从merged_by_type中提取uz支持的网盘链接
            const allUrls = []

            for (const [, links] of Object.entries(result.data.merged_by_type)) {
                if (!Array.isArray(links)) continue

                for (const linkInfo of links) {
                    if (!linkInfo || !linkInfo.url) continue

                    // 验证是否为uz支持的网盘链接
                    if (isValidPanUrl(linkInfo.url)) {
                        allUrls.push(linkInfo.url)
                    }
                }
            }

            return [...new Set(allUrls)]
        }

        return []

    } catch (error) {
        return []
    }
}





/**
 * 清理标题
 */
function cleanTitle(title) {
    if (!title) return ''

    return title
        .replace(/^(名称[：:])/, '')
        .replace(/\s+/g, ' ')
        .trim()
}

/**
 * 验证是否为有效的网盘URL
 */
function isValidPanUrl(url) {
    if (!url || typeof url !== 'string') return false

    for (const domain of panUrlsExt) {
        if (url.includes(domain)) {
            return true
        }
    }
    return false
}

/**
 * 识别网盘提供商
 */
function identifyProviders(urls) {
    const providers = new Set()

    for (const url of urls) {
        for (const provider of providerRegexMap) {
            if (provider.regex.test(url)) {
                providers.add(provider.name)
                break
            }
        }
    }

    return Array.from(providers)
}

/**
 * 清理标题
 */
function cleanTitle(title) {
    if (!title) return ''

    return title
        .replace(/^(名称[：:])/, '')
        .replace(/\s+/g, ' ')
        .trim()
}



/**
 * 计算标题相似度
 */
function calculateTitleSimilarity(title1, title2) {
    const norm1 = normalizeTitle(title1)
    const norm2 = normalizeTitle(title2)

    if (norm1 === norm2) return 1.0
    if (norm1.includes(norm2) || norm2.includes(norm1)) return 0.8

    // 简单的包含匹配
    const words1 = norm1.split(' ').filter(w => w.length > 1)
    const words2 = norm2.split(' ').filter(w => w.length > 1)

    let matchCount = 0
    for (const word1 of words1) {
        for (const word2 of words2) {
            if (word1.includes(word2) || word2.includes(word1)) {
                matchCount++
                break
            }
        }
    }

    const maxWords = Math.max(words1.length, words2.length)
    return maxWords > 0 ? matchCount / maxWords : 0
}

/**
 * 标题标准化
 */
function normalizeTitle(title) {
    if (!title) return ''

    return title
        .toLowerCase()
        .replace(/[【】\[\]()（）]/g, '') // 去除括号
        .replace(/第[一二三四五六七八九十\d]+季/g, '') // 去除季数
        .replace(/\d{4}/g, '') // 去除年份
        .replace(/[^\u4e00-\u9fa5a-z0-9\s]/g, '') // 只保留中文、英文、数字、空格
        .replace(/\s+/g, ' ') // 合并多个空格
        .trim()
}
