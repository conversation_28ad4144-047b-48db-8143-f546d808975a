//@name:[盘] 至臻
//@version:1
//@webSite:http://www.miqk.cc
//@remark:
//@order: B


// ============ 配置区域 ============
const siteConfig = {
    webSite: 'http://www.miqk.cc',
    isPan: true, // 是否为网盘资源站

    // 分类配置（留空则自动解析，也可手动配置）
    categories: [
        // 手动配置示例：
        // { id: '26', name: '臻彩严选' },
        // { id: '1', name: '至臻电影' },
        // { id: '2', name: '至臻剧集' },
        // { id: '3', name: '至臻动漫' },
        // { id: '4', name: '至臻综艺' },
        // { id: '5', name: '至臻短剧' },
        // { id: '24', name: '老剧计划' }
    ],

    // 自动解析分类的选择器（基于实际网站结构）
    categorySelectors: {
        container: '.nav-menu-items .nav-menu-item, .grid-items .grid-item', // 分类容器
        link: 'a', // 分类链接
        excludeKeywords: ['首页', 'home', '全部影片', '搜索', '个人使用', '我的观影记录'] // 排除的关键词
    },

    // URL模板配置（支持多种格式）
    urlTemplates: {
        // 格式1: 传统格式
        categoryList: '/index.php/vod/show/id/{categoryId}/page/{page}.html',
        search: '/index.php/vod/search/page/{page}/wd/{keyword}.html',
        detail: '/index.php/vod/detail/id/{id}.html',

        // 格式2: 简化格式（如 wobg.js 网站）
        categoryListAlt: '/vodshow/{categoryId}--------{page}---.html',
        searchAlt: '/vodsearch/{keyword}----------{page}---.html',
        detailAlt: '/voddetail/{id}.html'
    },

    // 选择器配置
    selectors: {
        // 首页选择器（支持多个fallback选择器）
        index: {
            containers: ['#main .module-item', '.module-item'], // 按优先级排序
            link: '.module-item-pic a',
            image: '.module-item-pic img',
            title: '.module-item-pic img',
            remark: '.module-item-text',
            year: '.module-item-caption span',
            maxItems: 24 // 首页最大显示数量
        },

        // 列表页选择器
        list: {
            container: '#main .module-item',
            link: '.module-item-pic a',
            image: '.module-item-pic img',
            title: '.module-item-pic img',
            remark: '.module-item-text',
            year: '.module-item-caption span'
        },

        // 详情页选择器
        detail: {
            title: '.page-title',
            image: '.mobile-play .lazyload',
            infoItems: '.video-info-items', // 改为选择整个信息容器
            panUrls: '.module-row-info p'
        },

        // 搜索页选择器
        search: {
            container: '.module-search-item',
            link: '.video-serial',
            image: '.module-item-pic > img'
        }
    },

    // 网盘链接识别关键词（与baseCode.js中的panUrls保持一致）
    panKeywords: [
        '189.cn', // 天翼
        '123684.com', // 123
        '123865.com',
        '123912.com',
        '123pan.com',
        '123pan.cn',
        'pan.quark.cn', // 夸克
        'drive.uc.cn' // uc
    ]
}

const appConfig = {
    _webSite: siteConfig.webSite,

    get webSite() {
        return this._webSite.endsWith('/')
            ? this._webSite.slice(0, -1)
            : this._webSite
    },
    set webSite(value) {
        this._webSite = value
    },

    _uzTag: '',
    get uzTag() {
        return this._uzTag
    },
    set uzTag(value) {
        this._uzTag = value
    },

    // 获取请求头
    get headers() {
        return {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.2 Safari/605.1.15'
        }
    }
}

// ============ 工具函数 ============
/**
 * 自动检测网站使用的URL格式
 */
let detectedUrlFormat = null

/**
 * 缓存解析的分类列表
 */
let cachedCategories = null

/**
 * 清除缓存（用于调试或网站结构变化时）
 */
function clearCache() {
    detectedUrlFormat = null
    cachedCategories = null
}

async function detectUrlFormat() {
    if (detectedUrlFormat) return detectedUrlFormat

    try {
        const response = await req(appConfig.webSite, {
            headers: appConfig.headers,
            timeout: 5000
        })

        if (response.data) {
            const $ = cheerio.load(response.data)

            // 检查是否存在 /vodtype/ 格式的链接
            if ($('a[href*="/vodtype/"]').length > 0) {
                detectedUrlFormat = 'alt'
                return 'alt'
            }

            // 检查是否存在 /vod/type/id/ 格式的链接
            if ($('a[href*="/vod/type/id/"]').length > 0) {
                detectedUrlFormat = 'standard'
                return 'standard'
            }
        }
    } catch (error) {
        // 检测失败时默认使用标准格式
    }

    detectedUrlFormat = 'standard'
    return 'standard'
}

/**
 * 构建完整URL（自动选择格式）
 */
async function buildUrl(templateKey, params = {}) {
    const format = await detectUrlFormat()

    let template
    if (format === 'alt' && siteConfig.urlTemplates[templateKey + 'Alt']) {
        template = siteConfig.urlTemplates[templateKey + 'Alt']
    } else {
        template = siteConfig.urlTemplates[templateKey]
    }

    let url = appConfig.webSite + template
    Object.keys(params).forEach(key => {
        url = url.replace(`{${key}}`, params[key])
    })
    return url
}

/**
 * 安全获取元素属性或文本
 */
function safeGet($element, selector, attr = null) {
    try {
        const target = selector ? $element.find(selector) : $element
        if (!target.length) return ''

        if (attr) {
            return target.attr(attr) || ''
        } else {
            return target.text().trim() || ''
        }
    } catch (error) {
        return ''
    }
}

/**
 * 解析视频列表项
 */
function parseVideoItem(element, selectors) {
    return {
        vod_id: safeGet(element, selectors.link, 'href'),
        vod_name: safeGet(element, selectors.title, 'alt') || safeGet(element, selectors.title),
        vod_pic: safeGet(element, selectors.image, 'data-src') || safeGet(element, selectors.image, 'src'),
        vod_remarks: safeGet(element, selectors.remark),
        vod_year: safeGet(element, selectors.year)
    }
}

/**
 * 通用视频列表解析函数（支持多个容器选择器和去重）
 */
function parseVideoList($, selectors, maxItems = null) {
    const videos = []
    const seenIds = new Set() // 用于去重的ID集合

    // 如果有多个容器选择器，按优先级尝试
    const containers = selectors.containers || [selectors.container]

    for (const containerSelector of containers) {
        const elements = $(containerSelector)
        if (elements.length > 0) {
            elements.each((_, element) => {
                const videoData = parseVideoItem($(element), selectors)

                // 过滤无效数据和重复数据
                if (videoData.vod_id && videoData.vod_name && !seenIds.has(videoData.vod_id)) {
                    seenIds.add(videoData.vod_id)
                    videos.push(videoData)
                }

                // 如果设置了最大数量限制，达到后停止
                if (maxItems && videos.length >= maxItems) {
                    return false // 跳出each循环
                }
            })

            // 找到内容后就不再尝试其他选择器
            if (videos.length > 0) {
                break
            }
        }
    }

    return maxItems ? videos.slice(0, maxItems) : videos
}

/**
 * 自动解析网站分类（带缓存）
 */
async function parseCategories() {
    // 如果已经缓存了分类，直接返回
    if (cachedCategories) {
        return cachedCategories
    }

    try {
        // dlog('开始解析分类，请求网站:', appConfig.webSite)

        // 添加超时设置防止卡死
        const response = await req(appConfig.webSite, {
            headers: appConfig.headers,
            timeout: 10000 // 10秒超时
        })

        if (!response.data) {
            // dlog('网站响应为空')
            return []
        }

        // dlog('网站响应成功，开始解析HTML')
        const $ = cheerio.load(response.data)

        const categories = []
        let processedCount = 0
        const maxCategories = 20 // 限制最大分类数量防止卡死

        // 支持多种分类链接格式
        const patterns = [
            '/vod/type/id/',  // 原格式：/index.php/vod/type/id/26.html
            '/vodtype/'       // 新格式：/vodtype/1.html
        ]

        let matchingLinks = $()
        patterns.forEach(pattern => {
            matchingLinks = matchingLinks.add($(`a[href*="${pattern}"]`))
        })

        // dlog(`找到 ${matchingLinks.length} 个匹配的链接`)

        // 如果没有找到任何匹配的链接，直接返回空数组
        if (matchingLinks.length === 0) {
            return []
        }

        matchingLinks.each((_, element) => {
            // 防止处理过多元素导致卡死
            if (processedCount >= maxCategories) {
                // dlog(`已处理 ${maxCategories} 个分类，停止解析`)
                return false // 跳出循环
            }

            processedCount++
            const href = $(element).attr('href') || ''

            // 优先使用title属性（效率最高且准确）
            let name = $(element).attr('title') || ''

            // 如果title为空，使用文本内容（处理导航菜单）
            if (!name) {
                name = $(element).text().trim()
            }

            // 过滤无效链接和排除关键词
            if (!href || !name ||
                siteConfig.categorySelectors.excludeKeywords.some(keyword =>
                    name.includes(keyword) || href.includes(keyword))) {
                return
            }

            // 提取分类ID（支持多种URL格式）
            let categoryId = null

            // 格式1: /index.php/vod/type/id/26.html
            let idMatch = href.match(/\/id\/(\d+)/)
            if (idMatch) {
                categoryId = idMatch[1]
            } else {
                // 格式2: /vodtype/1.html
                idMatch = href.match(/\/vodtype\/(\d+)/)
                if (idMatch) {
                    categoryId = idMatch[1]
                }
            }

            if (categoryId) {
                // 避免重复
                if (!categories.find(cat => cat.id === categoryId)) {
                    categories.push({ id: categoryId, name: name })
                }
            }
        })

        // 只在成功时输出关键信息
        if (categories.length > 0) {
            dlog(`✓ 解析完成，共找到 ${categories.length} 个分类`)
            // 缓存解析结果
            cachedCategories = categories
        }
        return categories
    } catch (error) {
        dlog('✗ 自动解析分类失败:', error)
        return []
    }
}

/**
 * 提取网盘链接
 */
function extractPanUrls(html) {
    const $ = cheerio.load(html)
    const panUrls = []

    // 查找所有链接
    $('a[href]').each((_, element) => {
        const href = $(element).attr('href') || ''
        if (siteConfig.panKeywords.some(keyword => href.includes(keyword))) {
            if (!panUrls.includes(href)) {
                panUrls.push(href)
            }
        }
    })

    // 查找文本中的链接
    $(siteConfig.selectors.detail.panUrls).each((_, element) => {
        const text = $(element).text() || ''
        siteConfig.panKeywords.forEach(keyword => {
            if (text.includes(keyword)) {
                const urlMatch = text.match(/(https?:\/\/[^\s]+)/g)
                if (urlMatch) {
                    urlMatch.forEach(url => {
                        if (!panUrls.includes(url)) {
                            panUrls.push(url)
                        }
                    })
                }
            }
        })
    })

    return panUrls
}

// ============ 主要API函数 ============
/**
 * 获取分类列表（自动解析）
 */
async function getClassList(args) {
    const backData = new RepVideoClassList()
    try {
        let categories = siteConfig.categories

        // 如果没有手动配置，尝试自动解析
        if (!categories || categories.length === 0) {
            // dlog('尝试自动解析分类...')
            categories = await parseCategories()

            if (!categories || categories.length === 0) {
                throw new Error('无法自动解析分类，请检查网站结构或手动配置 siteConfig.categories')
            }

            // dlog('自动解析到分类:', categories)
        } else {
            dlog('✓ 使用手动配置的分类:', categories.length + '个')
        }

        // 添加首页作为特殊分类
        const allCategories = [
            { id: 'index', name: '首页推荐' },
            ...categories
        ];
        
        // 记录日志
        dlog('✓ 添加首页分类，总共' + allCategories.length + '个分类');
        
        backData.data = allCategories.map(cat => ({
            type_id: cat.id,
            type_name: cat.name,
            hasSubclass: false
        }))
    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取子分类列表（暂不支持）
 */
async function getSubclassList(args) {
    const backData = new RepVideoSubclassList()
    return JSON.stringify(backData)
}

/**
 * 获取子分类视频列表（暂不支持）
 */
async function getSubclassVideoList(args) {
    const backData = new RepVideoList()
    return JSON.stringify(backData)
}

/**
 * 获取分类视频列表
 */
async function getVideoList(args) {
    const backData = new RepVideoList()
    try {
        // 首页分页特殊处理：首页只有第1页，其他页返回空
        if (args.url === 'index' && args.page > 1) {
            backData.data = []
            return JSON.stringify(backData)
        }

        // 统一URL构建逻辑
        const url = args.url === 'index'
            ? appConfig.webSite
            : await buildUrl('categoryList', {
                categoryId: args.url,
                page: args.page || 1
            })

        const response = await req(url, { headers: appConfig.headers })
        backData.error = response.error

        if (response.data) {
            const $ = cheerio.load(response.data)

            // 根据页面类型选择对应的选择器配置
            const selectors = args.url === 'index'
                ? siteConfig.selectors.index
                : siteConfig.selectors.list

            // 使用统一的解析函数
            const videos = parseVideoList($, selectors,
                args.url === 'index' ? selectors.maxItems : null)

            backData.data = videos
        }
    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 获取视频详情
 */
async function getVideoDetail(args) {
    const backData = new RepVideoDetail()
    try {
        const webUrl = appConfig.webSite + args.url
        const response = await req(webUrl, { headers: appConfig.headers })

        backData.error = response.error

        if (response.data) {
            const $ = cheerio.load(response.data)
            const vodDetail = new VideoDetail()
            vodDetail.vod_id = args.url

            // 获取标题
            const titleElement = $(siteConfig.selectors.detail.title).first()
            if (titleElement.length) {
                vodDetail.vod_name = titleElement.text().trim() ||
                    (titleElement[0].children && titleElement[0].children[0] ?
                     titleElement[0].children[0].data : '')
            }

            // 获取图片
            const imgElement = $(siteConfig.selectors.detail.image).first()
            if (imgElement.length) {
                vodDetail.vod_pic = imgElement.attr('data-src') || imgElement.attr('src') || ''
            }

            // 解析视频信息
            $(siteConfig.selectors.detail.infoItems).each((_, item) => {
                const $item = $(item)
                const titleElement = $item.find('.video-info-itemtitle')
                const key = titleElement.text().trim()

                if (key.includes('剧情')) {
                    // 优先获取完整剧情（sqjj_a），如果没有则获取简短剧情（zkjj_a）
                    let content = $item.find('.sqjj_a').text().trim()
                    if (!content) {
                        content = $item.find('.zkjj_a').text().trim()
                    }
                    // 移除展开/收起按钮文本
                    content = content.replace(/\[展开全部\]|\[收起部分\]/g, '').trim()
                    vodDetail.vod_content = content
                } else if (key.includes('导演') || key.includes('主演')) {
                    const links = $item.find('a')
                    const values = []
                    links.each((_, el) => {
                        const text = $(el).text().trim()
                        if (text) values.push(text)
                    })
                    // 使用斜杠分隔，符合图2所示格式
                    const value = values.join(' / ')

                    if (key.includes('导演')) {
                        // 仅保存导演信息，稍后会与主演信息一起格式化
                        vodDetail.vod_director = value
                    } else if (key.includes('主演')) {
                        // 仅保存主演信息，稍后会与导演信息一起格式化
                        vodDetail.vod_actor = value
                    }
                }
            })

            // 提取网盘链接
            if (siteConfig.isPan) {
                vodDetail.panUrls = extractPanUrls(response.data)
            }
            
            // 格式化导演和主演信息，添加换行符
            if (vodDetail.vod_director && vodDetail.vod_actor) {
                // 组合导演和主演信息，添加换行标记
                vodDetail.vod_actor = '导演：' + vodDetail.vod_director + '\n主演：' + vodDetail.vod_actor
                vodDetail.vod_director = '' // 清空导演字段，所有信息都放在vod_actor中
            }

            backData.data = vodDetail
        }
    } catch (error) {
        backData.error = '获取视频详情失败: ' + error.toString()
    }

    return JSON.stringify(backData)
}

/**
 * 获取视频播放地址（网盘资源站不需要实现）
 */
async function getVideoPlayUrl(args) {
    const backData = new RepVideoPlayUrl()
    // 网盘资源站不需要解析播放地址
    return JSON.stringify(backData)
}

/**
 * 搜索视频
 */
async function searchVideo(args) {
    const backData = new RepVideoList()
    try {
        const searchUrl = await buildUrl('search', {
            page: args.page || 1,
            keyword: encodeURIComponent(args.searchWord || '')
        })

        const response = await req(searchUrl, { headers: appConfig.headers })
        backData.error = response.error

        if (response.data) {
            const $ = cheerio.load(response.data)
            const videos = []

            $(siteConfig.selectors.search.container).each((_, element) => {
                try {
                    const linkElement = $(element).find(siteConfig.selectors.search.link).first()
                    const imgElement = $(element).find(siteConfig.selectors.search.image).first()

                    const video = new VideoDetail()
                    video.vod_id = linkElement.attr('href') || ''
                    video.vod_name = linkElement.attr('title') || linkElement.text().trim() || ''
                    video.vod_pic = imgElement.attr('data-src') || imgElement.attr('src') || ''
                    video.vod_remarks = linkElement.text().trim() || ''

                    // 过滤无效数据
                    if (video.vod_id && video.vod_name) {
                        videos.push(video)
                    }
                } catch (itemError) {
                    // 忽略单个项目解析错误，继续处理其他项目
                }
            })

            backData.data = videos
        }
    } catch (error) {
        backData.error = error.toString()
    }
    return JSON.stringify(backData)
}

/**
 * 调试日志函数
 */
function dlog(...args) {
    if (typeof UZUtils !== 'undefined' && UZUtils.debugLog) {
        UZUtils.debugLog(...args)
    } else {
        console.log('[Debug]', ...args)
    }
}
