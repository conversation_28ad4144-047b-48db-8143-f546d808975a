// ignore
//@name:自定义弹幕扩展
//@version:9
//@remark:v1.6.60 及以上版本可用
//@env:自定义弹幕解析接口##格式 线路名称1@地址1|线路名称2@地址2&&自定义弹幕屏蔽列表##使用|分隔，如：广告|推广|关注&&自定义弹幕采集线路##官采站点，格式 名称1@地址1|名称2@地址2
//@codeID:GdMl2GPT9ri1gcyvIPW7uF40AJbNHyu6
// ignore


// ==================== 常量定义 ====================
const REQUEST_TIMEOUT_MS = 20000 // 请求超时时间：20秒

// 字符串常量
const ENV_KEY_DANMU_LINES = '自定义弹幕解析接口' // 环境变量键名
const ENV_KEY_BLOCK_WORDS = '自定义弹幕屏蔽列表' // 自定义弹幕屏蔽列表环境变量键名
const ENV_KEY_CAIJI_SITES = '自定义弹幕采集线路' // 自定义弹幕采集线路环境变量键名
const LINE_SEPARATOR = '|' // 统一分隔符（自定义弹幕解析接口、自定义弹幕采集线路、屏蔽词）
const NAME_URL_SEPARATOR = '@' // 名称和URL分隔符
const PLAY_URL_SEPARATOR = '$$$' // 播放地址分隔符
const EPISODE_SEPARATOR = '#' // 集数分隔符
const NAME_URL_PAIR_SEPARATOR = '$' // 集数名称和URL分隔符

// 预编译的正则表达式
const SPECIAL_CONTENT_REGEX = /预告|花絮|预览|片花/ // 特殊内容过滤

// 默认的自定义弹幕屏蔽列表
const DEFAULT_BLOCK_WORDS = [
    'http', 'https', 'www', 'com', 'xyz', 'cn', 'net', 'org',
    '加群', '微信', 'QQ群', '关注', '订阅', '点赞', '投币', '收藏', '分享',
    '免费', '资源', '下载', '官网', 'APP'
]

// 默认的自定义弹幕采集线路
const DEFAULT_CAIJI_SITES = [
    { name: '小猫咪', url: 'https://zy.xmm.hk/api.php/provide/vod' },
    { name: '舒渡', url: 'https://www.69mu.cn/api.php/provide/vod' },
    { name: '听风', url: 'https://gctf.tfdh.top/api.php/provide/vod' }
]

// 默认的弹幕解析线路
const DEFAULT_DANMU_LINES = [
    { name: '虾米', url: 'https://dmku.hls.one/?ac=dm&url=' },
    { name: 'DouFun', url: 'https://danmu.56uxi.com/?ac=dm&url=' },
    { name: '弹幕库', url: 'https://api.danmu.icu/?ac=dm&url=' },
    { name: '678', url: 'https://se.678.ooo/?ac=dm&url=' }
]

// 动态弹幕解析线路（运行时生成）
let danmuLines = [
    { name: '智能', url: '' },
    { name: '快速', url: '' },
    ...DEFAULT_DANMU_LINES
]

// ==================== 全局变量 ====================
let caijiSitesWithNames = [] // 自定义弹幕采集线路（包含名称）
let BLOCK_LIST_REGEX = null // 弹幕内容屏蔽正则表达式

// ==================== 缓存变量 ====================
let configInitialized = false // 配置是否已初始化（软件生命周期内有效）



// ==================== 配置对象 ====================
const appConfig = {
    _uzTag: '',
    /**
     * 扩展标识，初次加载时，uz 会自动赋值，请勿修改
     * 用于读取环境变量
     */
    get uzTag() {
        return this._uzTag
    },
    set uzTag(value) {
        this._uzTag = value
    },
}

class DanMu {
    constructor() {
        /**
         * 弹幕内容
         * @type {string}
         */
        this.content = ''

        /**
         * 弹幕出现时间 单位秒
         * @type {number}
         */
        this.time = 0
    }
}

class BackData {
    constructor() {
        /**
         * 弹幕数据
         * @type {DanMu[]}
         */
        this.data = []

        /**
         * 错误信息
         * @type {string}
         */
        this.error = ''
    }
}

class SearchParameters {
    constructor() {
        /**
         * 动画或影片名称
         */
        this.name = ''
        /**
         * 动画或影片集数
         */
        this.episode = ''

        /**
         * 所在平台剧集链接
         * v1.6.60 及以上版本可用
         */
        this.videoUrl = ''

        /**
         * 自定义弹幕解析接口
         * v1.6.60 及以上版本可用
         */
        this.line = ''
    }
}

// ==================== 工具函数 ====================
/**
 * 转义正则表达式特殊字符
 * @param {string} string - 需要转义的字符串
 * @returns {string} - 转义后的字符串
 */
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

/**
 * 兼容解析分隔符（支持竖线|和分号;两种格式）
 * @param {string} str - 需要解析的字符串
 * @returns {string[]} - 解析后的数组
 */
function parseCompatibleSeparator(str) {
    if (!str || typeof str !== 'string') {
        return []
    }

    // 检测使用哪种分隔符（优先检测竖线，因为这是新格式）
    if (str.includes(LINE_SEPARATOR)) {
        return str.split(LINE_SEPARATOR)
    } else if (str.includes(';')) {
        // 兼容旧格式的分号分隔符
        return str.split(';')
    } else {
        // 如果都没有分隔符，返回单个元素的数组
        return [str]
    }
}

/**
 * 统一的参数验证函数
 * @param {SearchParameters} args - 搜索参数对象
 * @returns {string|null} - 返回错误信息，null表示验证通过
 */
function validateSearchArgs(args) {
    if (!args || typeof args !== 'object') {
        return '搜索参数无效'
    }

    if (!args.name || typeof args.name !== 'string') {
        return '请提供有效的视频名称'
    }

    if (args.episode == null || args.episode === '' ||
        (typeof args.episode !== 'string' && typeof args.episode !== 'number')) {
        return '请提供有效的集数'
    }

    return null // 验证通过
}



/**
 * 初始化自定义弹幕采集线路
 * 从环境变量获取自定义弹幕采集线路，如果没有则使用默认值
 * 使用内存缓存避免重复初始化（软件生命周期内有效）
 */
async function initCaijiSites() {
    // 检查是否已初始化（软件生命周期内只初始化一次）
    if (configInitialized && caijiSitesWithNames.length > 0) {
        return // 使用缓存的配置
    }

    let allSites = await getEnv(appConfig.uzTag, ENV_KEY_CAIJI_SITES)

    if (allSites.length > 0) {
        // 从环境变量获取自定义弹幕采集线路，兼容两种分隔符格式
        const userSitesWithNames = parseCompatibleSeparator(allSites)
            .map((item) => {
                const arr = item.split(NAME_URL_SEPARATOR)
                if (arr.length === 2 && arr[0].trim() && arr[1].trim()) {
                    return { name: arr[0].trim(), url: arr[1].trim() }
                }
                return null
            })
            .filter(Boolean)

        if (userSitesWithNames.length > 0) {
            caijiSitesWithNames = userSitesWithNames
        }
    } else {
        // 如果没有环境变量配置，设置默认的自定义弹幕采集线路到环境变量
        const defaultSitesStr = DEFAULT_CAIJI_SITES
            .map(site => `${site.name}${NAME_URL_SEPARATOR}${site.url}`)
            .join(LINE_SEPARATOR)

        await setEnv(appConfig.uzTag, ENV_KEY_CAIJI_SITES, defaultSitesStr)
        caijiSitesWithNames = [...DEFAULT_CAIJI_SITES]
    }


}

/**
 * 初始化自定义弹幕屏蔽列表正则表达式
 * 从环境变量获取屏蔽词，如果没有则使用默认值
 * 使用内存缓存避免重复初始化（软件生命周期内有效）
 */
async function initBlockWordsRegex() {
    // 检查是否已初始化（软件生命周期内只初始化一次）
    if (configInitialized && BLOCK_LIST_REGEX) {
        return // 使用缓存的配置
    }

    let blockWords = await getEnv(appConfig.uzTag, ENV_KEY_BLOCK_WORDS)
    let finalBlockWords = []

    if (blockWords.length > 0) {
        // 从环境变量获取屏蔽词，兼容两种分隔符格式
        finalBlockWords = parseCompatibleSeparator(blockWords)
            .map(word => word.trim())
            .filter(word => word.length > 0)
    } else {
        // 如果没有环境变量配置，设置默认的屏蔽词到环境变量
        const defaultBlockWordsStr = DEFAULT_BLOCK_WORDS.join(LINE_SEPARATOR)
        await setEnv(appConfig.uzTag, ENV_KEY_BLOCK_WORDS, defaultBlockWordsStr)
        finalBlockWords = DEFAULT_BLOCK_WORDS
    }

    // 生成正则表达式（转义特殊字符）
    if (finalBlockWords.length > 0) {
        BLOCK_LIST_REGEX = new RegExp(finalBlockWords.map(escapeRegExp).join('|'), 'i')
    } else {
        // 如果屏蔽词为空，创建一个永远不匹配的正则表达式
        BLOCK_LIST_REGEX = /(?!.*)/  // 负向前瞻，永远不匹配任何内容
    }

    // 更新缓存状态
    configInitialized = true
}



/**
 * 获取所有自定义弹幕解析接口，可选
 * v1.6.60 及以上版本可用
 * @returns {Promise<{lines: string[],error: string}>} result - 返回一个包含自定义弹幕解析接口列表的 Promise 对象
 */

async function getLines() {
    let error = ''
    let allLines = await getEnv(appConfig.uzTag, ENV_KEY_DANMU_LINES)

    // 智能模式和快速模式始终内置，不可更改
    danmuLines = [
        {
            name: '智能',
            url: '',
        },
        {
            name: '快速',
            url: '',
        }
    ]

    // 从环境变量获取用户配置的线路，兼容两种分隔符格式
    if (allLines.length > 0) {
        const userLines = parseCompatibleSeparator(allLines)
            .map((item) => {
                const arr = item.split(NAME_URL_SEPARATOR)
                if (arr.length === 2 && arr[0].trim() && arr[1].trim()) {
                    return {
                        name: arr[0].trim(),
                        url: arr[1].trim(),
                    }
                }
                return null
            })
            .filter(Boolean)

        danmuLines = danmuLines.concat(userLines)
    } else {
        // 如果没有环境变量配置，设置默认的自定义弹幕解析接口到环境变量
        const defaultLines = DEFAULT_DANMU_LINES
            .map(line => `${line.name}${NAME_URL_SEPARATOR}${line.url}`)
            .join(LINE_SEPARATOR)

        await setEnv(appConfig.uzTag, ENV_KEY_DANMU_LINES, defaultLines)

        // 重新解析默认线路
        danmuLines = [
            { name: '智能', url: '' },
            { name: '快速', url: '' },
            ...DEFAULT_DANMU_LINES
        ]
    }

    return formatBackData({
        lines: danmuLines.map((item) => item.name),
        error: error,
    })
}

/**
 * 搜索弹幕
 * @param {SearchParameters} args - 包含搜索参数的对象
 * @returns {Promise<BackData>} backData - 返回一个 Promise 对象
 */
async function searchDanMu(args) {
    let backData = new BackData()

    // 参数验证
    const validationError = validateSearchArgs(args)
    if (validationError) {
        backData.error = validationError
        return formatBackData(backData)
    }

    try {
        // 初始化自定义弹幕采集线路和屏蔽词正则表达式
        await initCaijiSites()
        await initBlockWordsRegex()
        // 首先获取视频播放地址
        const videoResult = await getVideoUrl(args)

        if (!videoResult.url || videoResult.url.length === 0) {
            backData.error = '未找到视频播放地址'
            return formatBackData(backData)
        }

        // 显示视频匹配结果的Toast通知
        if (videoResult.realName || videoResult.realEpisode) {
            const displayName = videoResult.realName || args.name
            const displayEpisode = videoResult.realEpisode || `第${args.episode}集`
            const caijiSite = videoResult.caijiSite || '未知'

            const videoToastMessage = `通过 ${caijiSite} - 匹配到 ${displayName} ${displayEpisode} - 弹幕解析中......`
            toast(videoToastMessage)
        }

        const videoUrl = videoResult.url

        // 获取自定义弹幕解析接口链接
        let selectedLineName = '智能'
        let lines
        let useFastMode = false

        if (args.line && args.line !== '智能' && args.line !== '快速') {
            const matchedLine = danmuLines.find(
                (line) => line.name === args.line
            )
            if (matchedLine) {
                lines = [{ name: matchedLine.name, url: matchedLine.url }]
                selectedLineName = matchedLine.name
            } else {
                // 如果指定的线路不存在，回退到智能模式
                lines = danmuLines.filter((item) => item.url.length > 0)
            }
        } else if (args.line === '快速') {
            // 快速模式：使用所有有效线路，但返回第一个有效结果
            lines = danmuLines.filter((item) => item.url.length > 0)
            selectedLineName = '快速'
            useFastMode = true
        } else {
            // 智能模式：使用所有有效线路，返回弹幕数最多的结果
            lines = danmuLines.filter((item) => item.url.length > 0)
        }

        // 并发请求所有自定义弹幕解析接口
        const promises = lines.map(async (lineInfo) => {
            try {
                let reqUrl = lineInfo.url + videoUrl
                const response = await req(reqUrl, {
                    responseType: ReqResponseType.plain,
                })
                if (response.data && typeof response.data === 'string') {
                    const danmuList = parseDanmuData(response.data)
                    if (danmuList.length > 0) {
                        return { danmuList, lineName: lineInfo.name }
                    }
                }
            } catch (error) {
                console.log(`自定义弹幕解析接口 ${lineInfo.name} 请求失败:`, error.toString())
            }
            return null
        })

        let bestResult = null

        if (useFastMode) {
            // 快速模式：返回第一个有效结果
            try {
                bestResult = await Promise.any(
                    promises.map(async (promise) => {
                        const result = await promise
                        if (result && result.danmuList && result.danmuList.length > 0) {
                            return result
                        }
                        throw new Error('无效结果')
                    })
                )
            } catch (aggregateError) {
                // 如果Promise.any失败，等待所有请求完成并返回第一个有效结果
                const results = await Promise.allSettled(promises)
                const validResults = results
                    .filter(result => result.status === 'fulfilled' && result.value && result.value.danmuList && result.value.danmuList.length > 0)
                    .map(result => result.value)

                bestResult = validResults.length > 0 ? validResults[0] : null
            }
        } else {
            // 智能模式：等待所有请求完成，获取弹幕数最多的结果
            const results = await Promise.all(promises)

            // 过滤有效结果并按弹幕数量排序（从多到少）
            const validResults = results
                .filter((r) => r && r.danmuList && r.danmuList.length > 0)
                .sort((a, b) => b.danmuList.length - a.danmuList.length)

            // 获取弹幕数最多的结果
            bestResult = validResults.length > 0 ? validResults[0] : null
        }

        if (bestResult && bestResult.danmuList) {
            backData.data = bestResult.danmuList

            // 显示弹幕解析完成的Toast通知
            const danmuSource = (selectedLineName === '智能' || selectedLineName === '快速') ? bestResult.lineName : selectedLineName
            const danmuToastMessage = `通过 ${danmuSource} - 解析到 ${bestResult.danmuList.length} 条弹幕`

            toast(danmuToastMessage, 3)
        } else {
            backData.data = []
        }
    } catch (error) {
        backData.error = error.toString()
    }

    if (backData.data.length === 0) {
        backData.error = backData.error || '未找到弹幕'
    }
    return formatBackData(backData)
}



/**
 * 弹幕内容过滤函数
 * @param {string} content - 弹幕内容
 * @returns {boolean} - true表示保留，false表示过滤
 */
function filterDanmuContent(content) {
    // 参数验证
    if (typeof content !== 'string') {
        return false // 非字符串内容直接过滤掉
    }

    // 使用预编译的正则表达式进行快速匹配
    return !BLOCK_LIST_REGEX.test(content)
}

/**
 * 解析弹幕数据
 * @param {string} data - 弹幕数据
 * @returns {DanMu[]} - 解析后的弹幕数组
 */
function parseDanmuData(data) {
    let danmuList = []

    // 参数验证
    if (typeof data !== 'string') {
        return danmuList // 返回空数组
    }

    try {
        data = data.trim()
        // 尝试解析 XML 格式弹幕
        if (data.includes('<d p=')) {
            const $ = cheerio.load(data, { xmlMode: true })
            $('d').each((_, element) => {
                const p = $(element).attr('p')
                if (p) {
                    const parts = p.split(',')
                    if (parts.length >= 1) {
                        const danmu = new DanMu()
                        danmu.time = parseFloat(parts[0]) || 0
                        danmu.content = $(element).text().trim()
                        // 应用弹幕内容过滤
                        if (danmu.content && filterDanmuContent(danmu.content)) {
                            danmuList.push(danmu)
                        }
                    }
                }
            })
        }
        // 尝试解析 JSON 格式弹幕
        else {
            const jsonData = JSON.parse(data)
            let danmuArray = jsonData?.danmuku ?? []

            // 动态检测数据开始位置，而不是硬编码跳过
            let startIndex = 0
            if (danmuArray.length > 0) {
                // 寻找第一个有效的弹幕数据项
                for (let i = 0; i < Math.min(danmuArray.length, 5); i++) {
                    if (Array.isArray(danmuArray[i]) && danmuArray[i].length >= 5) {
                        startIndex = i
                        break
                    }
                }
            }

            let single = danmuArray.slice(startIndex)
            const mappedDanmu = single.map((item) => {
                // 检查数组长度和数据类型，防止越界访问
                if (!Array.isArray(item) || item.length < 5) {
                    return null
                }

                // 验证时间和内容的类型
                const time = parseFloat(item[0])
                const content = String(item[4] || '').trim()

                if (isNaN(time) || !content) {
                    return null
                }

                let danMu = new DanMu()
                danMu.content = content
                danMu.time = time
                return danMu
            }).filter(Boolean) // 过滤掉null值

            single = mappedDanmu.filter((danMu) => filterDanmuContent(danMu.content))
            danmuList = danmuList.concat(single)
        }
    } catch (error) {
        console.log('解析弹幕数据失败:', error.toString())
    }
    return danmuList
}

/**
 * 获取视频的播放地址
 * @param {SearchParameters} args
 * @returns {Promise<{url: string, realName?: string, realEpisode?: string, caijiSite?: string}>}
 */
async function getVideoUrl(args) {
    // 参数验证
    const validationError = validateSearchArgs(args)
    if (validationError) {
        return { url: '' }
    }

    // 如果直接提供了视频URL，直接返回
    if (args.videoUrl && typeof args.videoUrl === 'string' && args.videoUrl.startsWith('http')) {
        return { url: args.videoUrl.trim() }
    }

    let videoName = args.name
    let episode = args.episode

    // 并发请求所有采集站，返回第一个有效结果
    const promises = caijiSitesWithNames.map(async (siteInfo) => {
        try {
            const searchUrl = `${siteInfo.url}/?ac=detail&wd=${videoName}&pg=1`
            console.log('搜索视频地址:', searchUrl)
            const response = await req(searchUrl)
            if (response.data) {
                const data = JSON.parse(response.data)
                if (data.list && data.list.length > 0) {
                    // 遍历所有搜索结果，找到名称匹配的视频
                    const video = data.list.find(v => v.vod_name === videoName)
                    if (video && video.vod_play_url) {
                        const lines = video.vod_play_url.split(PLAY_URL_SEPARATOR)
                        const targetEpisode = episode.toString()

                        // 第一轮：优先匹配集数名称
                        for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
                            const element = lines[lineIndex]
                            const eps = element.split(EPISODE_SEPARATOR)
                            for (let epIndex = 0; epIndex < eps.length; epIndex++) {
                                const ep = eps[epIndex]
                                const epArr = ep.split(NAME_URL_PAIR_SEPARATOR)
                                if (epArr.length === 2) {
                                    const epName = epArr[0]
                                    const epUrl = epArr[1]

                                    // 智能匹配集数名称（支持多种格式）
                                    const episodePatterns = [
                                        `第${targetEpisode}集`,
                                        `${targetEpisode}集`,
                                        targetEpisode,
                                        `EP${targetEpisode}`,
                                        `第${targetEpisode}话`,
                                        `${targetEpisode}话`
                                    ]

                                    if (episodePatterns.includes(epName)) {
                                        return {
                                            url: epUrl,
                                            realName: video.vod_name,
                                            realEpisode: epName,
                                            caijiSite: siteInfo.name
                                        }
                                    }
                                }
                            }
                        }

                        // 第二轮：回退到索引匹配，但排除特殊内容
                        for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
                            const element = lines[lineIndex]
                            const eps = element.split(EPISODE_SEPARATOR)
                            for (let epIndex = 0; epIndex < eps.length; epIndex++) {
                                const ep = eps[epIndex]
                                const epArr = ep.split(NAME_URL_PAIR_SEPARATOR)
                                if (epArr.length === 2) {
                                    const epName = epArr[0]
                                    const epUrl = epArr[1]

                                    // 改进的索引匹配逻辑，支持多种编号规则
                                    const episodeNum = parseInt(episode, 10)
                                    if (isNaN(episodeNum)) continue

                                    // 尝试多种索引匹配策略
                                    const indexMatches = [
                                        epIndex === episodeNum - 1,  // 从1开始编号
                                        epIndex === episodeNum,      // 从0开始编号
                                        epIndex === episodeNum + 1   // 特殊情况处理
                                    ]

                                    if (indexMatches.some(match => match) &&
                                        !SPECIAL_CONTENT_REGEX.test(epName)) {
                                        return {
                                            url: epUrl,
                                            realName: video.vod_name,
                                            realEpisode: epName,
                                            caijiSite: siteInfo.name
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.log(`采集站 ${siteInfo.name} 请求失败:`, error.toString())
        }
        return { url: '' }
    })

    // 使用Promise.any获取第一个成功的结果，而不是等待所有请求完成
    try {
        // 为每个请求添加超时处理
        const promisesWithTimeout = promises.map(promise =>
            Promise.race([
                promise,
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('单个请求超时')), REQUEST_TIMEOUT_MS)
                )
            ]).catch(error => ({ url: '', error: error.message }))
        )

        // 使用Promise.any获取第一个成功的结果
        try {
            const firstValidResult = await Promise.any(
                promisesWithTimeout.map(async (promise) => {
                    const result = await promise
                    if (result && result.url && result.url.length > 0) {
                        return result
                    }
                    throw new Error('无效结果')
                })
            )
            return firstValidResult
        } catch (aggregateError) {
            // 如果Promise.any失败，等待所有请求完成并返回第一个有效结果
            const results = await Promise.allSettled(promisesWithTimeout)
            const validResults = results
                .filter(result => result.status === 'fulfilled' && result.value.url && result.value.url.length > 0)
                .map(result => result.value)

            return validResults.length > 0 ? validResults[0] : { url: '' }
        }
    } catch (error) {
        console.log('获取视频地址失败:', error.toString())
        return { url: '' }
    }
}