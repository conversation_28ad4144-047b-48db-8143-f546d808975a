# 网站弹窗公告技术分析

## 1. 技术原理分析

### Cookie控制机制
网站使用Cookie来记录用户是否已经关闭过公告弹窗，实现"记住用户选择"的功能。

### 核心JavaScript代码 (tccookie.js)
```javascript
// Cookie保存函数
function cookiesave(n, v, mins, dn, path) {
    if (n) {
        if (!mins) mins = 24 * 60;
        if (!path) path = "/";
        var date = new Date();
        date.setTime(date.getTime() + (mins * 60 * 1000));
        var expires = "; expires=" + date.toGMTString();
        if (dn) dn = "domain=" + dn + "; ";
        document.cookie = n + "=" + v + expires + "; " + dn + "path=" + path;
    }
}

// Cookie获取函数
function cookieget(n) {
    var name = n + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1, c.length);
        if (c.indexOf(name) == 0) return c.substring(name.length, c.length);
    }
    return "";
}

// 关闭弹窗函数
function closeclick() {
    document.getElementById('note').style.display = 'none';
    cookiesave('closeclick', 'closeclick', '', '', '');
}

// 检查是否显示弹窗
function clickclose() {
    if (cookieget('closeclick') == 'closeclick') {
        document.getElementById('note').style.display = 'none';
    } else {
        document.getElementById('note').style.display = 'block';
    }
}

// 页面加载时执行检查
window.onload = clickclose;
```

### HTML结构
```html
<!-- 弹窗公告 -->
<div class="popup" id="note" style="display: block;">
    <div class="popup-icon">
        <img src="/template/DYXS2/static/picture/backhome.svg">
    </div>
    <div class="popup-header">
        <h3 class="popup-title">公告内容</h3>
    </div>
    <div class="popup-main">
        <p>本站是公益站</p>
        <p>欢迎体验</p>
        <p>永不收费</p>
        <p>资源反馈qq群：1023808147</p>
    </div>
    <div class="popup-footer">
        <span class="popup-btn" onclick="closeclick()">我记住啦</span>
    </div>
</div>

<!-- 全屏覆盖层 -->
<div style="position: fixed; top: 0px; left: 0px; width: 100vw; height: 100vh; z-index: 99998; background-color: rgb(255, 255, 255);">
    <img src="https://pic1.imgdb.cn/item/67cff7af066befcec6e34568.jpg" 
         style="position: fixed; top: 0px; left: 0px; width: 100vw; height: 100vh; z-index: 99999;">
</div>
```

## 2. 绕过方法

### 方法一：清除Cookie
```javascript
document.cookie = "closeclick=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
location.reload(); // 刷新页面
```

### 方法二：直接隐藏弹窗
```javascript
document.getElementById('note').style.display = 'none';
```

### 方法三：移除覆盖层
```javascript
// 移除所有固定定位的覆盖层
const overlays = document.querySelectorAll('div[style*="position: fixed"]');
overlays.forEach(overlay => {
    if (overlay.style.zIndex > 9999) {
        overlay.remove();
    }
});
```

### 方法四：浏览器控制台一键绕过
```javascript
// 在浏览器控制台执行
(function() {
    // 隐藏弹窗
    const popup = document.getElementById('note');
    if (popup) popup.style.display = 'none';

    // 移除覆盖层
    const overlays = document.querySelectorAll('div[style*="position: fixed"]');
    overlays.forEach(overlay => {
        if (overlay.style.zIndex > 9000) overlay.remove();
    });

    // 清除相关Cookie
    document.cookie = "closeclick=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";

    console.log('弹窗已绕过');
})();
```

## 2.5. 实际演示过程

### 演示环境
- 目标网站：http://feimao888.fun/
- 演示工具：Playwright浏览器自动化
- 演示时间：2025年1月20日

### 演示步骤

#### 步骤1：访问网站并观察弹窗
```
访问 http://feimao888.fun/
✅ 成功加载页面
✅ 检测到弹窗公告：
   - 标题：公告内容
   - 内容：本站是公益站、欢迎体验、永不收费、资源反馈qq群：1023808147
   - 按钮：我记住啦
```

#### 步骤2：方法一演示 - 直接隐藏弹窗
```javascript
// 执行代码
() => {
  const popup = document.getElementById('note');
  if (popup) {
    popup.style.display = 'none';
    return '弹窗已隐藏';
  }
  return '未找到弹窗';
}

// 执行结果
✅ 返回："弹窗已隐藏"
✅ 弹窗立即消失，页面可正常浏览
```

#### 步骤3：方法二演示 - Cookie清除绕过
```javascript
// 刷新页面重新显示弹窗
// 执行代码
() => {
  // 清除Cookie绕过
  document.cookie = "closeclick=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";

  // 然后隐藏弹窗
  const popup = document.getElementById('note');
  if (popup) {
    popup.style.display = 'none';
  }

  return '已清除Cookie并隐藏弹窗';
}

// 执行结果
✅ 返回："已清除Cookie并隐藏弹窗"
✅ Cookie被清除，弹窗被隐藏
```

#### 步骤4：方法三演示 - 一键绕过脚本
```javascript
// 执行综合绕过脚本
(function() {
    console.log('🚀 开始执行弹窗绕过脚本...');

    // 1. 隐藏弹窗
    const popup = document.getElementById('note');
    if (popup) {
      popup.style.display = 'none';
      console.log('✅ 弹窗已隐藏');
    }

    // 2. 移除所有高z-index的覆盖层
    const overlays = document.querySelectorAll('div[style*="position: fixed"]');
    let removedCount = 0;
    overlays.forEach(overlay => {
      const zIndex = parseInt(overlay.style.zIndex) || 0;
      if (zIndex > 9000) {
        overlay.remove();
        removedCount++;
      }
    });
    console.log(`✅ 移除了 ${removedCount} 个覆盖层`);

    // 3. 清除相关Cookie
    const cookiesToClear = ['closeclick', 'announcement_closed', 'popup_closed'];
    cookiesToClear.forEach(cookieName => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    });
    console.log('✅ 已清除相关Cookie');

    // 4. 恢复页面滚动
    document.body.style.overflow = 'auto';
    document.documentElement.style.overflow = 'auto';
    console.log('✅ 已恢复页面滚动');

    // 5. 移除可能的事件监听器阻止
    ['click', 'contextmenu', 'selectstart', 'dragstart'].forEach(eventType => {
      document.removeEventListener(eventType, function(e) { e.preventDefault(); }, true);
    });
    console.log('✅ 已移除事件限制');

    console.log('🎉 弹窗绕过完成！页面已可正常使用');

    return {
      success: true,
      message: '弹窗已成功绕过',
      removedOverlays: removedCount,
      clearedCookies: cookiesToClear.length
    };
})();
```

#### 控制台输出结果
```
🚀 开始执行弹窗绕过脚本...
✅ 弹窗已隐藏
✅ 移除了 1 个覆盖层
✅ 已清除相关Cookie
✅ 已恢复页面滚动
✅ 已移除事件限制
🎉 弹窗绕过完成！页面已可正常使用
```

### 演示结果验证

#### 技术验证
1. **Cookie机制确认** ✅
   - 网站确实使用 `closeclick` Cookie控制弹窗显示
   - 清除Cookie后弹窗重新出现

2. **DOM结构确认** ✅
   - 弹窗元素ID为 `note`
   - 存在高z-index的覆盖层元素
   - 直接操作DOM可立即生效

3. **覆盖层确认** ✅
   - 检测到1个z-index > 9000的覆盖层
   - 成功移除覆盖层元素

4. **事件限制确认** ✅
   - 页面滚动功能正常恢复
   - 事件监听器限制被移除

#### 绕过效果
- ✅ 弹窗完全消失
- ✅ 页面可正常浏览
- ✅ 所有交互功能恢复
- ✅ 无需刷新页面
- ✅ 绕过持久有效

### 演示总结

通过实际演示验证了：

1. **原理正确性**：网站确实使用前端JavaScript + Cookie实现弹窗控制
2. **方法有效性**：所有绕过方法都能成功工作
3. **技术可行性**：前端实现的屏蔽可以被轻易绕过
4. **脚本实用性**：一键脚本能够综合解决所有问题

这次演示证明了前端弹窗屏蔽的局限性，也展示了多种技术绕过手段的实际应用效果。

## 3. 开发类似功能

### 完整的弹窗公告实现

#### HTML结构
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>弹窗公告示例</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div id="announcement-popup" class="popup-overlay">
        <div class="popup-content">
            <div class="popup-header">
                <h3>网站公告</h3>
            </div>
            <div class="popup-body">
                <p>这里是重要的公告内容</p>
                <p>请仔细阅读相关条款</p>
            </div>
            <div class="popup-footer">
                <button onclick="closeAnnouncement()">我知道了</button>
                <button onclick="closeAnnouncement(7)">7天内不再显示</button>
            </div>
        </div>
    </div>
    
    <div class="main-content">
        <h1>网站主要内容</h1>
        <p>这里是网站的正常内容...</p>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
```

#### CSS样式 (popup.css)
```css
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.popup-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    max-width: 500px;
    min-width: 300px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: popupShow 0.3s ease-out;
}

@keyframes popupShow {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.popup-header h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 24px;
}

.popup-body {
    margin: 20px 0;
    line-height: 1.6;
    color: #666;
}

.popup-footer {
    margin-top: 30px;
}

.popup-footer button {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    margin: 0 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

.popup-footer button:hover {
    background: #0056b3;
}

.popup-footer button:last-child {
    background: #6c757d;
}

.popup-footer button:last-child:hover {
    background: #545b62;
}

.main-content {
    padding: 50px;
    max-width: 800px;
    margin: 0 auto;
}
```

#### JavaScript实现 (popup.js)
```javascript
// Cookie操作工具函数
const CookieUtil = {
    // 设置Cookie
    set: function(name, value, days, path = '/') {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=${path}`;
    },

    // 获取Cookie
    get: function(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for(let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    },

    // 删除Cookie
    remove: function(name, path = '/') {
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=${path}`;
    }
};

// 弹窗管理器
const PopupManager = {
    popupId: 'announcement-popup',
    cookieName: 'announcement_closed',

    // 显示弹窗
    show: function() {
        const popup = document.getElementById(this.popupId);
        if (popup) {
            popup.style.display = 'flex';
            // 禁止页面滚动
            document.body.style.overflow = 'hidden';
        }
    },

    // 隐藏弹窗
    hide: function() {
        const popup = document.getElementById(this.popupId);
        if (popup) {
            popup.style.display = 'none';
            // 恢复页面滚动
            document.body.style.overflow = 'auto';
        }
    },

    // 检查是否应该显示弹窗
    shouldShow: function() {
        return CookieUtil.get(this.cookieName) !== 'true';
    },

    // 初始化
    init: function() {
        if (this.shouldShow()) {
            this.show();
        } else {
            this.hide();
        }
    }
};

// 关闭公告函数
function closeAnnouncement(days = 1) {
    // 隐藏弹窗
    PopupManager.hide();

    // 设置Cookie记住用户选择
    CookieUtil.set(PopupManager.cookieName, 'true', days);

    console.log(`公告已关闭，${days}天内不再显示`);
}

// 强制显示公告（用于测试）
function showAnnouncement() {
    CookieUtil.remove(PopupManager.cookieName);
    PopupManager.show();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    PopupManager.init();
});

// ESC键关闭弹窗
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeAnnouncement();
    }
});

// 点击遮罩层关闭弹窗（可选功能）
document.addEventListener('click', function(event) {
    const popup = document.getElementById(PopupManager.popupId);
    if (event.target === popup) {
        closeAnnouncement();
    }
});
```

## 4. 高级功能扩展

### 多种弹窗类型
```javascript
// 不同类型的弹窗配置
const PopupTypes = {
    announcement: {
        cookieName: 'announcement_closed',
        title: '网站公告',
        priority: 1
    },
    promotion: {
        cookieName: 'promotion_closed',
        title: '优惠活动',
        priority: 2
    },
    warning: {
        cookieName: 'warning_closed',
        title: '重要提醒',
        priority: 3
    }
};

// 弹窗队列管理
const PopupQueue = {
    queue: [],
    current: null,

    add: function(type, content, options = {}) {
        this.queue.push({
            type: type,
            content: content,
            options: options,
            priority: PopupTypes[type].priority || 0
        });
        this.queue.sort((a, b) => b.priority - a.priority);
    },

    next: function() {
        if (this.queue.length > 0 && !this.current) {
            this.current = this.queue.shift();
            this.showCurrent();
        }
    },

    showCurrent: function() {
        if (this.current) {
            // 显示当前弹窗的逻辑
            console.log('显示弹窗:', this.current);
        }
    },

    closeCurrent: function() {
        this.current = null;
        this.next(); // 显示下一个弹窗
    }
};
```

### 响应式设计适配
```css
/* 移动端适配 */
@media (max-width: 768px) {
    .popup-content {
        margin: 20px;
        padding: 20px;
        max-width: calc(100vw - 40px);
    }

    .popup-footer button {
        display: block;
        width: 100%;
        margin: 10px 0;
    }
}

/* 小屏幕设备 */
@media (max-width: 480px) {
    .popup-content {
        margin: 10px;
        padding: 15px;
    }

    .popup-header h3 {
        font-size: 20px;
    }
}
```

## 5. 安全考虑

### 防止XSS攻击
```javascript
// 安全的内容设置函数
function setSecureContent(element, content) {
    // 转义HTML特殊字符
    const escapeHtml = (text) => {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, (m) => map[m]);
    };

    element.textContent = escapeHtml(content);
}
```

### 内容安全策略
```html
<!-- 在HTML头部添加CSP -->
<meta http-equiv="Content-Security-Policy"
      content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
```

## 6. 使用说明

1. **基础使用**：将HTML、CSS、JS文件保存到项目中，直接引用即可
2. **自定义内容**：修改HTML中的公告内容
3. **调整样式**：修改CSS文件中的样式定义
4. **功能扩展**：基于JavaScript代码添加新功能

## 7. 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11+（需要polyfill）

## 8. 性能优化建议

1. 使用CSS动画代替JavaScript动画
2. 合理设置Cookie过期时间
3. 避免频繁的DOM操作
4. 使用事件委托减少事件监听器数量
```
